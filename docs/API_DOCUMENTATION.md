# API 文件

## 基本資訊

- **基礎 URL**: `http://localhost:8000`
- **API 版本**: v1
- **認證方式**: Bearer Token
- **資料格式**: JSON

## 自動生成的 API 文件

FastAPI 自動生成互動式 API 文件：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API 端點概覽

### 認證相關 (`/api/auth`)

| 方法 | 端點 | 描述 |
|------|------|------|
| POST | `/api/auth/login` | 使用者登入 |
| POST | `/api/auth/logout` | 使用者登出 |
| GET | `/api/auth/me` | 取得當前使用者資訊 |
| GET | `/api/auth/permissions` | 取得使用者權限 |

### 客戶訂貨 (`/api/customer-orders`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/customer-orders` | 取得訂單列表 |
| POST | `/api/customer-orders` | 建立新訂單 |
| GET | `/api/customer-orders/{id}` | 取得特定訂單 |
| PUT | `/api/customer-orders/{id}` | 更新訂單 |
| DELETE | `/api/customer-orders/{id}` | 刪除訂單 |
| GET | `/api/customer-orders/{id}/progress` | 取得訂單進度 |

### 生產排程 (`/api/production-schedule`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/production-schedule/workstations` | 取得工作站列表 |
| GET | `/api/production-schedule/schedule` | 取得日排程 |
| GET | `/api/production-schedule/tasks` | 取得生產任務 |
| POST | `/api/production-schedule/tasks` | 建立生產任務 |
| GET | `/api/production-schedule/analytics/utilization` | 取得使用率分析 |

### 原物料訂購 (`/api/material-orders`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/material-orders` | 取得原物料訂單 |
| POST | `/api/material-orders` | 建立原物料訂單 |
| GET | `/api/material-orders/{id}` | 取得特定訂單 |
| GET | `/api/material-orders/suppliers` | 取得供應商列表 |

### 倉儲進銷存 (`/api/inventory`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/inventory/items` | 取得庫存項目 |
| GET | `/api/inventory/transactions` | 取得庫存異動 |
| POST | `/api/inventory/transactions` | 建立庫存異動 |
| GET | `/api/inventory/reports/stock-level` | 庫存水準報表 |
| GET | `/api/inventory/alerts/low-stock` | 低庫存警示 |

### 財務 (`/api/finance`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/finance/reports/{period}` | 取得財務報表 |
| GET | `/api/finance/receivables` | 應收帳款 |
| GET | `/api/finance/payables` | 應付帳款 |
| GET | `/api/finance/cash-flow` | 現金流量報表 |

### 人事薪資 (`/api/hr-payroll`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/hr-payroll/employees` | 取得員工列表 |
| POST | `/api/hr-payroll/employees` | 建立員工資料 |
| GET | `/api/hr-payroll/attendance` | 取得出勤記錄 |
| POST | `/api/hr-payroll/attendance/check-in` | 上班打卡 |
| POST | `/api/hr-payroll/attendance/check-out` | 下班打卡 |
| GET | `/api/hr-payroll/payroll` | 取得薪資記錄 |

### 商品資料 (`/api/product-data`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/product-data` | 取得商品列表 |
| POST | `/api/product-data` | 建立商品資料 |
| GET | `/api/product-data/{id}` | 取得特定商品 |
| GET | `/api/product-data/{id}/cost-analysis` | 成本分析 |
| GET | `/api/product-data/{id}/bom` | BOM 表 |
| GET | `/api/product-data/categories` | 商品分類 |

### 客戶管理 (`/api/customer-management`)

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/customer-management` | 取得客戶列表 |
| POST | `/api/customer-management` | 建立客戶資料 |
| GET | `/api/customer-management/{id}` | 取得特定客戶 |
| GET | `/api/customer-management/{id}/sales-analysis` | 銷售分析 |
| GET | `/api/customer-management/{id}/product-analysis` | 產品分析 |

## 認證流程

### 1. 登入
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**回應:**
```json
{
  "access_token": "mock-token-admin-1234567890",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 2. 使用 Token
在後續的 API 請求中，在 Header 中加入：
```http
Authorization: Bearer mock-token-admin-1234567890
```

## 錯誤處理

### HTTP 狀態碼
- `200` - 成功
- `201` - 建立成功
- `400` - 請求錯誤
- `401` - 未認證
- `403` - 權限不足
- `404` - 資源不存在
- `500` - 伺服器錯誤

### 錯誤回應格式
```json
{
  "message": "錯誤描述",
  "detail": "詳細錯誤資訊"
}
```

## 分頁參數

大部分列表 API 支援分頁參數：

- `skip`: 跳過的項目數量 (預設: 0)
- `limit`: 每頁項目數量 (預設: 100, 最大: 1000)

**範例:**
```http
GET /api/customer-orders?skip=0&limit=20
```

## 篩選參數

許多 API 支援篩選參數：

**客戶訂單篩選:**
- `status`: 訂單狀態
- `customer_id`: 客戶 ID
- `start_date`: 開始日期
- `end_date`: 結束日期

**範例:**
```http
GET /api/customer-orders?status=production&start_date=2024-01-01&end_date=2024-01-31
```

## 資料模型範例

### 客戶訂單
```json
{
  "id": 1,
  "customer_id": 1,
  "customer_name": "ABC公司",
  "order_date": "2024-01-10T00:00:00",
  "delivery_date": "2024-01-25T00:00:00",
  "status": "production",
  "total_amount": 15000.0,
  "items": [
    {
      "product_id": 1,
      "product_name": "包裝盒A",
      "quantity": 1000,
      "unit_price": 15.0,
      "total_price": 15000.0,
      "specifications": "300x200x100mm"
    }
  ],
  "notes": "急件",
  "created_at": "2024-01-10T10:00:00",
  "updated_at": "2024-01-10T10:00:00"
}
```

### 工作站狀態
```json
{
  "workstation": "printing_1",
  "name": "印刷機1",
  "vietnamese_name": "Máy in",
  "current_task": {
    "id": 1,
    "order_id": 1,
    "customer_name": "ABC公司",
    "product_name": "包裝盒A",
    "quantity": 1000,
    "priority": 1,
    "estimated_hours": 4.0,
    "status": "in_progress"
  },
  "pending_tasks": 3,
  "daily_capacity_hours": 8.0,
  "utilization_rate": 75.0
}
```

## 開發注意事項

1. **所有時間格式**: 使用 ISO 8601 格式 (`YYYY-MM-DDTHH:MM:SS`)
2. **貨幣金額**: 使用浮點數，單位為新台幣
3. **ID 欄位**: 使用整數自增 ID
4. **布林值**: 使用 `true`/`false`
5. **空值**: 使用 `null`

## 測試工具

推薦使用以下工具測試 API：
- **Swagger UI**: http://localhost:8000/docs (內建)
- **Postman**: 匯入 OpenAPI 規格
- **curl**: 命令列工具
- **HTTPie**: 友善的命令列工具
