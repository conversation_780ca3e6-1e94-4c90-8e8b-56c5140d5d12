# 順興公司 ERP 系統概覽

## 系統架構圖

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   前端 (React)   │ ◄─────────────────► │ 後端 (FastAPI)   │
│                 │                     │                 │
│ • React 18      │                     │ • FastAPI       │
│ • TypeScript    │                     │ • Python 3.13  │
│ • Ant Design    │                     │ • Pydantic      │
│ • Vite          │                     │ • Uvicorn       │
│ • i18next       │                     │ • CORS          │
└─────────────────┘                     └─────────────────┘
        │                                        │
        │                                        │
        ▼                                        ▼
┌─────────────────┐                     ┌─────────────────┐
│   瀏覽器存儲     │                     │   未來擴展       │
│                 │                     │                 │
│ • localStorage  │                     │ • PostgreSQL    │
│ • sessionStorage│                     │ • Redis         │
│ • Cookies       │                     │ • Celery        │
└─────────────────┘                     └─────────────────┘
```

## ERP 模組架構

### 1. 客戶訂貨模組 (Customer Orders)
- **功能**: 客戶訂單管理與追蹤進度
- **API 端點**: `/api/customer-orders`
- **主要功能**:
  - 訂單建立、修改、刪除
  - 訂單狀態追蹤
  - 進度查詢
  - 客戶訂單歷史

### 2. 原物料訂購模組 (Material Orders)
- **功能**: 原物料採購管理
- **API 端點**: `/api/material-orders`
- **主要功能**:
  - 供應商管理
  - 採購訂單管理
  - 交貨狀態追蹤

### 3. 倉儲進銷存模組 (Inventory)
- **功能**: 庫存管理系統
- **API 端點**: `/api/inventory`
- **主要功能**:
  - 庫存項目管理
  - 進銷存記錄
  - 庫存報表
  - 低庫存警示

### 4. 財務模組 (Finance)
- **功能**: 財務管理與報表
- **API 端點**: `/api/finance`
- **主要功能**:
  - 年月週日報表
  - 應收應付帳款
  - 現金流量分析
  - 獲利分析

### 5. 人事薪資模組 (HR & Payroll)
- **功能**: 人事管理與薪資計算
- **API 端點**: `/api/hr-payroll`
- **主要功能**:
  - 員工資料管理
  - 上下班打卡
  - 薪資計算
  - 出勤統計

### 6. 商品資料管理 (Product Data)
- **功能**: 商品與成本管理
- **API 端點**: `/api/product-data`
- **主要功能**:
  - 商品資料維護
  - BOM 表管理
  - 成本計算
  - 售價管理

### 7. 生產排程模組 (Production Schedule)
- **功能**: 生產計劃與工作站管理
- **API 端點**: `/api/production-schedule`
- **工作站列表**:
  1. 印刷機1 (Máy in)
  2. 印刷機2 (Máy Đỏ)
  3. 貼合機 (Máy Dán)
  4. 壓合機 (Máy Dán Hộp)
  5. 手貼機 (máy Dán Nhỏ)
  6. 打釘機1 (Máy Đóng Định 1)
  7. 打釘機2 (Máy Đóng Định 2)
  8. 打釘機3 (Máy Đóng Định 3)
  9. 壓痕機1 (Máy Bế 1)
  10. 壓痕機2 (Máy Bế 2)
  11. 覆膜機 (Máy qua mạng)
  12. 分紙機 (Máy Xá)
  13. 手工區1 (Dán Tay1)
  14. 手工區2 (Dán Tay2)

### 8. 客戶管理模組 (Customer Management)
- **功能**: 客戶關係管理
- **API 端點**: `/api/customer-management`
- **主要功能**:
  - 客戶資料管理
  - 銷售分析
  - 產品分析
  - 客戶信用管理

### 9. 權限管理系統 (Authentication & Authorization)
- **功能**: 使用者認證與權限控制
- **API 端點**: `/api/auth`
- **權限等級**:
  - **admin**: 全部權限
  - **manager**: 管理層權限
  - **production**: 生產相關權限
  - **hr**: 人事相關權限
  - **user**: 基本使用者權限

## 多語系支援

### 支援語言
- **繁體中文 (zh-TW)**: 預設語言
- **越南文 (vi-VN)**: 第二語言

### 實現方式
- 使用 react-i18next 框架
- JSON 格式翻譯檔案
- 動態語言切換
- 瀏覽器語言偵測
- localStorage 語言記憶

## 安全性考量

### 認證機制
- 基於 Token 的認證 (未來將實現 JWT)
- HTTP Bearer Token
- 登入狀態管理

### API 安全
- CORS 配置
- 請求驗證
- 錯誤處理
- 輸入驗證 (Pydantic)

## 效能優化

### 前端優化
- Vite 快速建置
- 代碼分割 (Code Splitting)
- 懶加載 (Lazy Loading)
- 組件優化

### 後端優化
- FastAPI 高效能框架
- 異步處理支援
- 自動 API 文件生成
- 資料驗證快取

## 未來擴展計劃

### 資料庫整合
- PostgreSQL 主資料庫
- Redis 快取系統
- 資料遷移工具

### 進階功能
- 即時通知系統
- 檔案上傳管理
- 報表匯出功能
- 行動端支援

### 部署與維運
- Docker 容器化
- CI/CD 自動化部署
- 監控與日誌系統
- 備份與災難恢復
