# 📋 ERP 系統使用說明

## 🚀 **系統啟動**

### 1. 啟動後端服務
```bash
cd backend
source .venv/bin/activate
python3 main.py
```
後端服務將在 `http://localhost:8000` 運行

### 2. 啟動前端服務
```bash
cd frontend
npm start
```
前端服務將在 `http://localhost:3000` 運行

## 📊 **頁面結構整理**

### 🏠 **主要功能模組**

#### 1. **業務管理** 📈
- **商品報價管理** (`/product-quotation`) ⭐ **主要使用**
  - 對應 Excel 商品資料欄位
  - 包含：報價時間、客戶、編號、分類、規格、材質、成本、利潤等
  - 支援分頁、搜尋、篩選
  
- **訂單管理** (`/order-management`) ⭐ **主要使用**
  - 對應 Excel 訂單資料欄位
  - 包含：訂單編號、客戶、產品、數量、生產狀態、交貨進度等
  - 支援分頁、搜尋、篩選
  
- **客戶管理** (`/customer-management`)
  - 客戶基本資料管理
  - 支援分頁、搜尋

#### 2. **生產管理** 🏭
- **生產排程** (`/production-schedule`)
- **原料採購** (`/material-orders`)
- **庫存管理** (`/inventory`)

#### 3. **數據分析** 📊
- **客戶分析** (`/customer-analysis`)
- **財務管理** (`/finance`)

#### 4. **人事管理** 👥
- **人事薪資** (`/hr-payroll`)

## 📋 **已匯入的資料**

### ✅ **成功匯入的 Excel 資料**
- **商品資料**: 1,397 筆 (來自 `商品資料.xlsx`)
- **訂單資料**: 2,716 筆 (來自 `客戶訂單.xlsx`)
- **客戶資料**: 65 筆 (自動創建)

### 📁 **Excel 檔案對應**
- `商品資料.xlsx` → **商品報價管理**頁面
- `客戶訂單.xlsx` → **訂單管理**頁面

## 🎯 **主要使用流程**

### 1. **查看商品報價** 
1. 進入 **業務管理** → **商品報價管理**
2. 可以看到所有從 Excel 匯入的商品資料
3. 支援按客戶、分類、日期範圍篩選
4. 支援搜尋編號或產品名稱

### 2. **查看訂單資料**
1. 進入 **業務管理** → **訂單管理**
2. 可以看到所有從 Excel 匯入的訂單資料
3. 支援按客戶、生產狀態篩選
4. 可以查看訂單完成進度

### 3. **管理客戶資料**
1. 進入 **業務管理** → **客戶管理**
2. 查看所有客戶基本資料
3. 支援搜尋和篩選

## 🔧 **功能特色**

### ✨ **Excel 完全對應**
- 前端欄位完全對應 Excel 結構
- 支援越南文和中文雙語欄位
- 保留原始 Excel 的所有重要資訊

### 📄 **分頁功能**
- 每頁顯示 50 筆資料 (可調整)
- 支援快速跳頁
- 顯示總筆數和當前範圍

### 🔍 **搜尋篩選**
- 即時搜尋功能
- 多條件篩選
- 日期範圍篩選

### 📊 **資料視覺化**
- 利潤率顏色標示
- 生產進度條
- 狀態標籤

## 🗂️ **舊頁面處理**

### ❌ **已整合的舊頁面**
- `/product-data` → 重定向到 `/product-quotation`
- `/customer-orders` → 重定向到 `/order-management`

### ⚠️ **注意事項**
- 舊的書籤或連結會自動重定向到新頁面
- 所有功能都已遷移到新的頁面結構
- 資料完全保留，無需重新匯入

## 📱 **使用建議**

### 🎯 **日常使用**
1. **主要使用新的業務管理模組**
2. **商品報價管理**：查看和管理所有商品報價
3. **訂單管理**：追蹤訂單狀態和生產進度
4. **客戶管理**：維護客戶基本資料

### 🔄 **資料更新**
- 如需匯入新的 Excel 資料，使用 `backend/excel_import.py`
- 支援增量更新，不會覆蓋現有資料

### 🚀 **效能優化**
- 使用分頁載入，避免一次載入過多資料
- 搜尋和篩選在後端執行，響應速度快
- 支援大量資料處理

## 🆘 **常見問題**

### Q: 為什麼有些欄位顯示為空？
A: Excel 中的空白欄位會顯示為 "-"，這是正常現象

### Q: 如何匯入新的 Excel 資料？
A: 使用 `python3 backend/excel_import.py "檔案路徑.xlsx" [type]`

### Q: 舊的頁面還能使用嗎？
A: 舊頁面會自動重定向到新頁面，功能完全保留

### Q: 資料會丟失嗎？
A: 不會，所有資料都已安全遷移到新結構

---

## 🎉 **開始使用**

現在您可以：
1. 啟動系統 (`http://localhost:3000`)
2. 進入 **業務管理** 模組
3. 開始使用 **商品報價管理** 和 **訂單管理**
4. 享受完全對應 Excel 結構的便利操作！
