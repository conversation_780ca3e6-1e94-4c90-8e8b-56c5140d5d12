# 順興公司 ERP 系統

## 專案概述
順興公司 ERP 系統是一個前後端分離的企業資源規劃系統，支援中文/越南文多語系介面。

## 技術架構
- **前端**: React + Vite + TypeScript
- **後端**: Python FastAPI
- **套件管理**: uv (Python), npm (Node.js)
- **多語系**: i18next (前端)

## 專案結構
```
ERP/
├── frontend/          # 前端 React 應用
├── backend/           # 後端 FastAPI 應用
├── docs/             # 專案文件
└── README.md         # 專案說明
```

## ERP 系統模組
1. **客戶訂貨模組** - 客戶訂單管理與追蹤進度
2. **原物料訂購模組** - 原物料採購管理
3. **倉儲進銷存模組** - 庫存管理系統
4. **財務模組** - 年月週日報表
5. **人事薪資模組** - 上下班時間與薪資計算
6. **商品資料管理** - 成本計算、BOM表、售價管理
7. **生產排程模組** - 14個工作站排程管理
8. **客戶管理模組** - 客戶資料與產品分析
9. **權限管理系統** - 使用者權限控制

## 生產排程工作站
- 印刷機1 (Máy in)
- 印刷機2 (Máy Đỏ)
- 貼合機 (Máy Dán)
- 壓合機 (Máy Dán Hộp)
- 手貼機 (máy Dán Nhỏ)
- 打釘機1 (Máy Đóng Định 1)
- 打釘機2 (Máy Đóng Định 2)
- 打釘機3 (Máy Đóng Định 3)
- 壓痕機1 (Máy Bế 1)
- 壓痕機2 (Máy Bế 2)
- 覆膜機 (Máy qua mạng)
- 分紙機 (Máy Xá)
- 手工區1 (Dán Tay1)
- 手工區2 (Dán Tay2)

## 開發環境設定

### 快速啟動
使用提供的批次檔案快速啟動系統：

**Windows:**
```bash
# 啟動後端 (在一個終端機中)
start-backend.bat

# 啟動前端 (在另一個終端機中)
start-frontend.bat
```

### 手動設定

#### 後端設定
```bash
# 進入後端目錄
cd backend

# 建立虛擬環境 (如果尚未建立)
uv venv
# 或使用 python -m venv .venv

# 啟動虛擬環境
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安裝依賴
pip install -r requirements.txt

# 啟動開發伺服器
python main.py
```

#### 前端設定
```bash
# 進入前端目錄
cd frontend

# 安裝依賴 (如果尚未安裝)
npm install

# 啟動開發伺服器
npm run dev
```

## 系統存取

### 網址
- **前端應用**: http://localhost:5173
- **後端 API**: http://localhost:8000
- **API 文件**: http://localhost:8000/docs (Swagger UI)
- **API 文件 (ReDoc)**: http://localhost:8000/redoc

### 測試帳號
- **帳號**: admin
- **密碼**: admin123

## 功能特色

### 已實現功能
✅ 使用者認證與權限管理
✅ 多語系支援 (中文/越南文)
✅ 響應式設計 (支援手機、平板、桌面)
✅ 客戶訂貨管理基礎功能
✅ 生產排程 14 個工作站管理
✅ 儀表板數據展示
✅ API 路由架構完整建立

### 待開發功能
🔄 資料庫整合
🔄 完整的 CRUD 操作
🔄 報表生成功能
🔄 檔案上傳與管理
🔄 即時通知系統
🔄 資料匯入匯出

## 技術架構

### 前端技術棧
- **框架**: React 18 + TypeScript
- **建置工具**: Vite
- **UI 框架**: Ant Design
- **狀態管理**: React Hooks
- **路由**: React Router v6
- **HTTP 客戶端**: Axios
- **多語系**: react-i18next

### 後端技術棧
- **框架**: FastAPI (Python)
- **ASGI 伺服器**: Uvicorn
- **API 文件**: Swagger UI / ReDoc
- **資料驗證**: Pydantic
- **CORS 支援**: FastAPI CORS middleware

## 多語系支援
- **繁體中文** (zh-TW) - 預設語言
- **越南文** (vi-VN)
- 支援動態語言切換
- 完整的 UI 翻譯

## 開發指南

### 新增 API 路由
1. 在 `backend/routers/` 目錄下建立新的路由檔案
2. 在 `backend/main.py` 中註冊新路由
3. 在 `frontend/src/services/api.ts` 中新增對應的 API 呼叫

### 新增前端頁面
1. 在 `frontend/src/pages/` 目錄下建立新頁面組件
2. 在 `frontend/src/App.tsx` 中新增路由
3. 在 `frontend/src/components/Layout.tsx` 中新增選單項目
4. 更新多語系翻譯檔案

### 新增翻譯
1. 編輯 `frontend/src/locales/zh-TW.json` (中文)
2. 編輯 `frontend/src/locales/vi-VN.json` (越南文)
3. 在組件中使用 `useTranslation` hook

## 專案結構說明
```
ERP/
├── backend/                 # 後端 FastAPI 應用
│   ├── routers/            # API 路由模組
│   ├── main.py             # 主應用程式
│   ├── requirements.txt    # Python 依賴
│   └── .venv/              # Python 虛擬環境
├── frontend/               # 前端 React 應用
│   ├── src/
│   │   ├── components/     # React 組件
│   │   ├── pages/          # 頁面組件
│   │   ├── services/       # API 服務
│   │   ├── locales/        # 多語系翻譯檔案
│   │   └── i18n/           # 多語系配置
│   ├── package.json        # Node.js 依賴
│   └── vite.config.ts      # Vite 配置
├── docs/                   # 專案文件
├── start-backend.bat       # 後端啟動腳本
├── start-frontend.bat      # 前端啟動腳本
└── README.md               # 專案說明
```
