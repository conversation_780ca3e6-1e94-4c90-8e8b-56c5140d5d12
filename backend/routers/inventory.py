from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func
from database import get_db, InventoryItem as DBInventoryItem, StockTransaction as DBStockTransaction, TransactionType
import math

router = APIRouter()

class InventoryItemBase(BaseModel):
    item_code: str
    item_name: str
    category: str
    unit: str
    current_stock: float
    min_stock: float
    max_stock: float
    unit_cost: float
    location: str
    supplier: str

class InventoryItemCreate(InventoryItemBase):
    pass

class InventoryItem(InventoryItemBase):
    id: int
    total_value: float
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class StockTransactionBase(BaseModel):
    item_id: int
    transaction_type: TransactionType
    quantity: float
    unit_cost: float
    reference_no: Optional[str] = None
    notes: Optional[str] = None

class StockTransactionCreate(StockTransactionBase):
    pass

class StockTransaction(StockTransactionBase):
    id: int
    created_at: datetime
    item_name: str

    class Config:
        orm_mode = True

@router.get("/items", response_model=List[InventoryItem])
async def get_inventory_items(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = None,
    low_stock: bool = False,
    db: Session = Depends(get_db)
):
    """取得庫存項目列表"""
    query = db.query(DBInventoryItem)
    
    # 過濾器
    if category:
        query = query.filter(DBInventoryItem.category == category)
    
    if low_stock:
        query = query.filter(DBInventoryItem.current_stock <= DBInventoryItem.min_stock)
    
    # 分頁
    total = query.count()
    items = query.offset(skip).limit(limit).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / limit) if limit > 0 else 0
    
    return items

@router.post("/items", response_model=InventoryItem, status_code=status.HTTP_201_CREATED)
async def create_inventory_item(item: InventoryItemCreate, db: Session = Depends(get_db)):
    """建立庫存項目"""
    # 檢查物料代碼是否已存在
    existing_item = db.query(DBInventoryItem).filter(DBInventoryItem.item_code == item.item_code).first()
    if existing_item:
        raise HTTPException(status_code=400, detail="物料代碼已存在")
    
    # 計算總價值
    total_value = item.current_stock * item.unit_cost
    
    # 建立新項目
    db_item = DBInventoryItem(
        **item.dict(),
        total_value=total_value
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

@router.get("/items/{item_id}", response_model=InventoryItem)
async def get_inventory_item(item_id: int, db: Session = Depends(get_db)):
    """獲取特定庫存項目"""
    item = db.query(DBInventoryItem).filter(DBInventoryItem.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="庫存項目未找到")
    return item

@router.put("/items/{item_id}", response_model=InventoryItem)
async def update_inventory_item(item_id: int, item_update: InventoryItemBase, db: Session = Depends(get_db)):
    """更新庫存項目"""
    db_item = db.query(DBInventoryItem).filter(DBInventoryItem.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="庫存項目未找到")
    
    # 更新屬性
    for key, value in item_update.dict().items():
        setattr(db_item, key, value)
    
    # 重新計算總價值
    db_item.total_value = db_item.current_stock * db_item.unit_cost
    
    db.commit()
    db.refresh(db_item)
    return db_item

@router.delete("/items/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_inventory_item(item_id: int, db: Session = Depends(get_db)):
    """刪除庫存項目"""
    db_item = db.query(DBInventoryItem).filter(DBInventoryItem.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="庫存項目未找到")
    
    db.delete(db_item)
    db.commit()
    return {"detail": "項目已刪除"}

@router.get("/transactions", response_model=List[StockTransaction])
async def get_stock_transactions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    item_id: Optional[int] = None,
    transaction_type: Optional[TransactionType] = None,
    db: Session = Depends(get_db)
):
    """取得庫存異動記錄"""
    query = db.query(
        DBStockTransaction.id,
        DBStockTransaction.item_id,
        DBStockTransaction.transaction_type,
        DBStockTransaction.quantity,
        DBStockTransaction.unit_cost,
        DBStockTransaction.reference_no,
        DBStockTransaction.notes,
        DBStockTransaction.created_at,
        DBInventoryItem.item_name
    ).join(DBInventoryItem)
    
    # 過濾器
    if item_id:
        query = query.filter(DBStockTransaction.item_id == item_id)
    
    if transaction_type:
        query = query.filter(DBStockTransaction.transaction_type == transaction_type)
    
    # 排序
    query = query.order_by(DBStockTransaction.created_at.desc())
    
    # 分頁
    transactions = query.offset(skip).limit(limit).all()
    
    # 轉換為 Pydantic 模型
    result = []
    for t in transactions:
        result.append({
            "id": t.id,
            "item_id": t.item_id,
            "item_name": t.item_name,
            "transaction_type": t.transaction_type,
            "quantity": t.quantity,
            "unit_cost": t.unit_cost,
            "reference_no": t.reference_no,
            "notes": t.notes,
            "created_at": t.created_at
        })
    
    return result

@router.post("/transactions", status_code=status.HTTP_201_CREATED)
async def create_stock_transaction(
    transaction: StockTransactionCreate,
    db: Session = Depends(get_db)
):
    """建立庫存異動記錄"""
    # 檢查項目是否存在
    item = db.query(DBInventoryItem).filter(DBInventoryItem.id == transaction.item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="庫存項目未找到")
    
    # 新建交易記錄
    db_transaction = DBStockTransaction(**transaction.dict())
    db.add(db_transaction)
    
    # 更新庫存數量
    new_stock = item.current_stock
    if transaction.transaction_type == TransactionType.IN:
        new_stock += transaction.quantity
    elif transaction.transaction_type == TransactionType.OUT:
        if item.current_stock < transaction.quantity:
            raise HTTPException(status_code=400, detail="庫存不足")
        new_stock -= transaction.quantity
    else:  # 調整
        new_stock = transaction.quantity
    
    # 更新庫存項目
    item.current_stock = new_stock
    item.total_value = new_stock * item.unit_cost
    item.updated_at = datetime.now()
    
    db.commit()
    db.refresh(db_transaction)
    
    return {"id": db_transaction.id, "message": "庫存異動記錄已建立"}

@router.get("/reports/stock-level")
async def get_stock_level_report(db: Session = Depends(get_db)):
    """取得庫存水準報表"""
    # 庫存狀態統計
    low_stock = db.query(DBInventoryItem).filter(
        DBInventoryItem.current_stock <= DBInventoryItem.min_stock
    ).count()
    
    excess_stock = db.query(DBInventoryItem).filter(
        DBInventoryItem.current_stock >= DBInventoryItem.max_stock
    ).count()
    
    normal_stock = db.query(DBInventoryItem).filter(
        DBInventoryItem.current_stock > DBInventoryItem.min_stock,
        DBInventoryItem.current_stock < DBInventoryItem.max_stock
    ).count()
    
    # 庫存總價值
    total_value = db.query(
        func.sum(DBInventoryItem.total_value)
    ).scalar() or 0

    # 依類別的庫存統計
    category_stats = db.query(
        DBInventoryItem.category,
        func.count(DBInventoryItem.id).label("count"),
        func.sum(DBInventoryItem.total_value).label("value")
    ).group_by(DBInventoryItem.category).all()
    
    category_data = []
    for stat in category_stats:
        category_data.append({
            "category": stat.category,
            "count": stat.count,
            "value": stat.value or 0
        })
    
    return {
        "total_items": low_stock + excess_stock + normal_stock,
        "low_stock_items": low_stock,
        "excess_stock_items": excess_stock,
        "normal_stock_items": normal_stock,
        "total_value": total_value,
        "category_statistics": category_data
    }

@router.get("/alerts/low-stock")
async def get_low_stock_alerts(db: Session = Depends(get_db)):
    """取得低庫存警示"""
    low_stock_items = db.query(DBInventoryItem).filter(
        DBInventoryItem.current_stock <= DBInventoryItem.min_stock
    ).all()
    
    alerts = []
    for item in low_stock_items:
        alerts.append({
            "id": item.id,
            "item_code": item.item_code,
            "item_name": item.item_name,
            "current_stock": item.current_stock,
            "min_stock": item.min_stock,
            "unit": item.unit,
            "shortage": item.min_stock - item.current_stock if item.current_stock < item.min_stock else 0
        })
    
    return alerts

@router.get("/categories")
async def get_categories(db: Session = Depends(get_db)):
    """取得所有庫存分類"""
    categories = db.query(DBInventoryItem.category).distinct().all()
    return [cat.category for cat in categories]

@router.get("/suppliers")
async def get_suppliers(db: Session = Depends(get_db)):
    """取得所有供應商"""
    suppliers = db.query(DBInventoryItem.supplier).distinct().all()
    return [sup.supplier for sup in suppliers]

@router.get("/locations")
async def get_locations(db: Session = Depends(get_db)):
    """取得所有庫存位置"""
    locations = db.query(DBInventoryItem.location).distinct().all()
    return [loc.location for loc in locations]
