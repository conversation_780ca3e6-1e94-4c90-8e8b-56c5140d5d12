from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, OrderManagement as DBOrderManagement
import math

router = APIRouter()

# Pydantic 模型
class OrderManagementBase(BaseModel):
    order_number: str
    customer_code: Optional[str] = None
    order_received_date: Optional[date] = None
    delivery_deadline: Optional[date] = None
    product_name: Optional[str] = None
    product_category: Optional[str] = None
    specifications: Optional[str] = None
    unit: str = "PCS"
    ordered_quantity: Optional[int] = None
    paper_order_quantity: Optional[int] = None
    actual_paper_quantity: Optional[int] = None
    printing_plate: Optional[str] = None
    mold_number: Optional[str] = None
    production_status: str = "pending"
    unit_price: Optional[float] = None
    total_amount: Optional[float] = None
    delivered_quantity: int = 0
    remaining_quantity: Optional[int] = None
    delivery_note_number: Optional[str] = None
    notes: Optional[str] = None
    order_status: str = "active"
    priority: str = "normal"
    created_by: Optional[str] = None

class OrderManagementCreate(OrderManagementBase):
    pass

class OrderManagementUpdate(BaseModel):
    order_number: Optional[str] = None
    customer_code: Optional[str] = None
    order_received_date: Optional[date] = None
    delivery_deadline: Optional[date] = None
    product_name: Optional[str] = None
    product_category: Optional[str] = None
    specifications: Optional[str] = None
    unit: Optional[str] = None
    ordered_quantity: Optional[int] = None
    paper_order_quantity: Optional[int] = None
    actual_paper_quantity: Optional[int] = None
    printing_plate: Optional[str] = None
    mold_number: Optional[str] = None
    production_status: Optional[str] = None
    unit_price: Optional[float] = None
    total_amount: Optional[float] = None
    delivered_quantity: Optional[int] = None
    remaining_quantity: Optional[int] = None
    delivery_note_number: Optional[str] = None
    notes: Optional[str] = None
    order_status: Optional[str] = None
    priority: Optional[str] = None

class OrderManagement(OrderManagementBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaginatedOrderManagementResponse(BaseModel):
    data: List[OrderManagement]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedOrderManagementResponse)
@router.get("", response_model=PaginatedOrderManagementResponse)
async def get_orders(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    order_status: Optional[str] = None,
    production_status: Optional[str] = None,
    priority: Optional[str] = None,
    customer_code: Optional[str] = None,
    delivery_deadline: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """取得訂單列表"""
    query = db.query(DBOrderManagement)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBOrderManagement.order_number.contains(search)) |
            (DBOrderManagement.product_name.contains(search)) |
            (DBOrderManagement.customer_code.contains(search)) |
            (DBOrderManagement.mold_number.contains(search))
        )
    
    if order_status:
        query = query.filter(DBOrderManagement.order_status == order_status)
    
    if production_status:
        query = query.filter(DBOrderManagement.production_status == production_status)
    
    if priority:
        query = query.filter(DBOrderManagement.priority == priority)
        
    if customer_code:
        query = query.filter(DBOrderManagement.customer_code.contains(customer_code))
        
    if delivery_deadline:
        query = query.filter(DBOrderManagement.delivery_deadline == delivery_deadline)

    # 計算總數
    total = query.count()
    
    # 分頁 - 按訂單編號排序
    offset = (page - 1) * pageSize
    orders = query.order_by(DBOrderManagement.order_number.desc()).offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedOrderManagementResponse(
        data=orders,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{order_id}", response_model=OrderManagement)
@router.get("/{order_id}/", response_model=OrderManagement)
async def get_order(order_id: int, db: Session = Depends(get_db)):
    """取得單一訂單資料"""
    order = db.query(DBOrderManagement).filter(DBOrderManagement.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")
    return order

@router.post("/", response_model=OrderManagement, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=OrderManagement, status_code=status.HTTP_201_CREATED)
async def create_order(order: OrderManagementCreate, db: Session = Depends(get_db)):
    """建立訂單"""
    # 檢查訂單編號是否已存在
    existing_order = db.query(DBOrderManagement).filter(DBOrderManagement.order_number == order.order_number).first()
    if existing_order:
        raise HTTPException(status_code=400, detail="訂單編號已存在")

    # 計算剩餘數量
    remaining_quantity = (order.ordered_quantity or 0) - (order.delivered_quantity or 0)
    
    # 計算總金額
    total_amount = (order.ordered_quantity or 0) * (order.unit_price or 0)

    # 創建新訂單
    order_data = order.dict()
    order_data['remaining_quantity'] = remaining_quantity
    order_data['total_amount'] = total_amount
    
    db_order = DBOrderManagement(**order_data)
    db.add(db_order)
    db.commit()
    db.refresh(db_order)
    return db_order

@router.put("/{order_id}", response_model=OrderManagement)
@router.put("/{order_id}/", response_model=OrderManagement)
async def update_order(order_id: int, order_update: OrderManagementUpdate, db: Session = Depends(get_db)):
    """更新訂單"""
    order = db.query(DBOrderManagement).filter(DBOrderManagement.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 如果更新訂單編號，檢查是否與其他訂單重複
    if order_update.order_number and order_update.order_number != order.order_number:
        existing_order = db.query(DBOrderManagement).filter(
            DBOrderManagement.order_number == order_update.order_number,
            DBOrderManagement.id != order_id
        ).first()
        if existing_order:
            raise HTTPException(status_code=400, detail="訂單編號已存在")

    # 更新欄位
    update_data = order_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(order, field, value)

    # 重新計算剩餘數量和總金額
    if order_update.ordered_quantity is not None or order_update.delivered_quantity is not None:
        order.remaining_quantity = (order.ordered_quantity or 0) - (order.delivered_quantity or 0)
    
    if order_update.ordered_quantity is not None or order_update.unit_price is not None:
        order.total_amount = (order.ordered_quantity or 0) * (order.unit_price or 0)

    db.commit()
    db.refresh(order)
    return order

@router.delete("/{order_id}")
@router.delete("/{order_id}/")
async def delete_order(order_id: int, db: Session = Depends(get_db)):
    """刪除訂單"""
    order = db.query(DBOrderManagement).filter(DBOrderManagement.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    db.delete(order)
    db.commit()
    
    return {"message": "訂單已刪除"}

@router.get("/customers/list")
async def get_customers_list(db: Session = Depends(get_db)):
    """取得所有活躍客戶列表"""
    from database import Customer as DBCustomer
    customers = db.query(DBCustomer).filter(DBCustomer.is_active == True).all()
    return [
        {
            "id": customer.id,
            "customer_code": customer.customer_code,
            "company_name": customer.company_name,
            "contact_person": customer.contact_person,
            "display_name": f"{customer.customer_code} - {customer.company_name}"
        }
        for customer in customers
    ]

@router.get("/categories/list")
async def get_categories_list(db: Session = Depends(get_db)):
    """取得所有品項列表"""
    categories = db.query(DBOrderManagement.product_category).distinct().filter(DBOrderManagement.product_category.isnot(None)).all()
    return [c.product_category for c in categories if c.product_category]

@router.post("/{order_id}/deliver")
async def deliver_order(order_id: int, delivery_quantity: int, delivery_note: Optional[str] = None, db: Session = Depends(get_db)):
    """交貨"""
    order = db.query(DBOrderManagement).filter(DBOrderManagement.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 檢查交貨數量是否超過剩餘數量
    if delivery_quantity > order.remaining_quantity:
        raise HTTPException(status_code=400, detail="交貨數量不能超過剩餘數量")

    # 更新交貨數量和剩餘數量
    order.delivered_quantity = (order.delivered_quantity or 0) + delivery_quantity
    order.remaining_quantity = (order.ordered_quantity or 0) - order.delivered_quantity
    
    if delivery_note:
        order.delivery_note_number = delivery_note
    
    # 如果全部交貨完成，更新訂單狀態
    if order.remaining_quantity <= 0:
        order.order_status = "completed"
    
    db.commit()
    db.refresh(order)
    
    return order

@router.get("/dashboard/summary")
async def get_order_summary(db: Session = Depends(get_db)):
    """取得訂單概況統計"""
    total = db.query(DBOrderManagement).count()
    active = db.query(DBOrderManagement).filter(DBOrderManagement.order_status == "active").count()
    completed = db.query(DBOrderManagement).filter(DBOrderManagement.order_status == "completed").count()
    cancelled = db.query(DBOrderManagement).filter(DBOrderManagement.order_status == "cancelled").count()
    
    # 生產狀態統計
    pending_production = db.query(DBOrderManagement).filter(DBOrderManagement.production_status == "pending").count()
    in_production = db.query(DBOrderManagement).filter(DBOrderManagement.production_status == "in_progress").count()
    production_completed = db.query(DBOrderManagement).filter(DBOrderManagement.production_status == "completed").count()
    
    # 總金額統計
    total_amount = db.query(DBOrderManagement.total_amount).filter(DBOrderManagement.order_status == "active").all()
    total_value = sum([amount[0] for amount in total_amount if amount[0]])
    
    return {
        "total_orders": total,
        "active_orders": active,
        "completed_orders": completed,
        "cancelled_orders": cancelled,
        "completion_rate": round((completed / total * 100) if total > 0 else 0, 1),
        "pending_production": pending_production,
        "in_production": in_production,
        "production_completed": production_completed,
        "total_value": total_value
    }
