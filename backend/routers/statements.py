from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, Statement as DBStatement, StatementItem as DBStatementItem, Customer as DBCustomer
import math

router = APIRouter()

# Pydantic 模型
class StatementItemBase(BaseModel):
    sequence_no: int
    delivery_date: Optional[date] = None
    delivery_note_no: Optional[str] = None
    product_name: str
    product_name_vn: Optional[str] = None
    specifications: Optional[str] = None
    unit: str = "PCS"
    quantity: float
    unit_price: float
    amount: float
    notes: Optional[str] = None

class StatementItemCreate(StatementItemBase):
    pass

class StatementItem(StatementItemBase):
    id: int
    statement_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class StatementBase(BaseModel):
    customer_id: int
    customer_code: str
    period_start: date
    period_end: date
    subtotal: float = 0.0
    tax_rate: float = 0.08
    tax_amount: float = 0.0
    total_amount: float = 0.0
    status: str = "draft"
    customer_confirmed: bool = False

class StatementCreate(StatementBase):
    items: List[StatementItemCreate] = []

class StatementUpdate(BaseModel):
    customer_id: Optional[int] = None
    customer_code: Optional[str] = None
    period_start: Optional[date] = None
    period_end: Optional[date] = None
    subtotal: Optional[float] = None
    tax_rate: Optional[float] = None
    tax_amount: Optional[float] = None
    total_amount: Optional[float] = None
    status: Optional[str] = None
    customer_confirmed: Optional[bool] = None
    items: Optional[List[StatementItemCreate]] = None

class Statement(StatementBase):
    id: int
    statement_no: str
    created_at: datetime
    updated_at: datetime
    items: List[StatementItem] = []
    # 客戶資訊
    customer_name: Optional[str] = None
    customer_address: Optional[str] = None
    customer_tax_id: Optional[str] = None
    customer_phone: Optional[str] = None

    class Config:
        from_attributes = True

class PaginatedStatementResponse(BaseModel):
    data: List[Statement]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedStatementResponse)
@router.get("", response_model=PaginatedStatementResponse)
async def get_statements(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    status: Optional[str] = None,
    customer_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """取得對帳單列表"""
    query = db.query(DBStatement).join(DBCustomer)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBStatement.statement_no.contains(search)) |
            (DBStatement.customer_code.contains(search)) |
            (DBCustomer.company_name.contains(search))
        )
    
    if status:
        query = query.filter(DBStatement.status == status)
    
    if customer_id:
        query = query.filter(DBStatement.customer_id == customer_id)

    # 計算總數
    total = query.count()
    
    # 分頁
    offset = (page - 1) * pageSize
    statements = query.offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    # 格式化回傳資料
    result = []
    for statement in statements:
        customer = db.query(DBCustomer).filter(DBCustomer.id == statement.customer_id).first()
        statement_data = {
            "id": statement.id,
            "statement_no": statement.statement_no,
            "customer_id": statement.customer_id,
            "customer_code": statement.customer_code,
            "period_start": statement.period_start,
            "period_end": statement.period_end,
            "subtotal": statement.subtotal,
            "tax_rate": statement.tax_rate,
            "tax_amount": statement.tax_amount,
            "total_amount": statement.total_amount,
            "status": statement.status,
            "customer_confirmed": statement.customer_confirmed,
            "created_at": statement.created_at,
            "updated_at": statement.updated_at,
            "items": statement.items,
            "customer_name": customer.company_name if customer else None,
            "customer_address": customer.address if customer else None,
            "customer_tax_id": customer.tax_id if customer else None,
            "customer_phone": customer.phone if customer else None,
        }
        result.append(statement_data)

    return PaginatedStatementResponse(
        data=result,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{statement_id}", response_model=Statement)
@router.get("/{statement_id}/", response_model=Statement)
async def get_statement(statement_id: int, db: Session = Depends(get_db)):
    """取得單一對帳單資料"""
    statement = db.query(DBStatement).filter(DBStatement.id == statement_id).first()
    if not statement:
        raise HTTPException(status_code=404, detail="對帳單不存在")
    
    # 獲取客戶資訊
    customer = db.query(DBCustomer).filter(DBCustomer.id == statement.customer_id).first()
    
    statement_data = {
        "id": statement.id,
        "statement_no": statement.statement_no,
        "customer_id": statement.customer_id,
        "customer_code": statement.customer_code,
        "period_start": statement.period_start,
        "period_end": statement.period_end,
        "subtotal": statement.subtotal,
        "tax_rate": statement.tax_rate,
        "tax_amount": statement.tax_amount,
        "total_amount": statement.total_amount,
        "status": statement.status,
        "customer_confirmed": statement.customer_confirmed,
        "created_at": statement.created_at,
        "updated_at": statement.updated_at,
        "items": statement.items,
        "customer_name": customer.company_name if customer else None,
        "customer_address": customer.address if customer else None,
        "customer_tax_id": customer.tax_id if customer else None,
        "customer_phone": customer.phone if customer else None,
    }
    
    return statement_data

@router.post("/", response_model=Statement, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=Statement, status_code=status.HTTP_201_CREATED)
async def create_statement(statement: StatementCreate, db: Session = Depends(get_db)):
    """建立對帳單"""
    # 生成對帳單號
    statement_count = db.query(DBStatement).count()
    statement_no = f"ST-{statement_count + 1:06d}"

    # 計算金額
    subtotal = sum(item.amount for item in statement.items)
    tax_amount = subtotal * statement.tax_rate
    total_amount = subtotal + tax_amount

    # 創建對帳單
    db_statement = DBStatement(
        statement_no=statement_no,
        customer_id=statement.customer_id,
        customer_code=statement.customer_code,
        period_start=statement.period_start,
        period_end=statement.period_end,
        subtotal=subtotal,
        tax_rate=statement.tax_rate,
        tax_amount=tax_amount,
        total_amount=total_amount,
        status=statement.status,
        customer_confirmed=statement.customer_confirmed
    )
    db.add(db_statement)
    db.commit()
    db.refresh(db_statement)

    # 創建對帳單項目
    for item_data in statement.items:
        db_item = DBStatementItem(
            statement_id=db_statement.id,
            **item_data.dict()
        )
        db.add(db_item)
    
    db.commit()
    db.refresh(db_statement)
    
    return await get_statement(db_statement.id, db)

@router.put("/{statement_id}", response_model=Statement)
@router.put("/{statement_id}/", response_model=Statement)
async def update_statement(statement_id: int, statement_update: StatementUpdate, db: Session = Depends(get_db)):
    """更新對帳單"""
    statement = db.query(DBStatement).filter(DBStatement.id == statement_id).first()
    if not statement:
        raise HTTPException(status_code=404, detail="對帳單不存在")

    # 更新基本資料
    update_data = statement_update.dict(exclude_unset=True, exclude={'items'})
    for field, value in update_data.items():
        setattr(statement, field, value)

    # 更新項目
    if statement_update.items is not None:
        # 刪除舊項目
        db.query(DBStatementItem).filter(DBStatementItem.statement_id == statement_id).delete()
        
        # 創建新項目
        for item_data in statement_update.items:
            db_item = DBStatementItem(
                statement_id=statement_id,
                **item_data.dict()
            )
            db.add(db_item)
        
        # 重新計算金額
        subtotal = sum(item.amount for item in statement_update.items)
        tax_amount = subtotal * statement.tax_rate
        total_amount = subtotal + tax_amount
        
        statement.subtotal = subtotal
        statement.tax_amount = tax_amount
        statement.total_amount = total_amount

    db.commit()
    db.refresh(statement)
    
    return await get_statement(statement_id, db)

@router.delete("/{statement_id}")
@router.delete("/{statement_id}/")
async def delete_statement(statement_id: int, db: Session = Depends(get_db)):
    """刪除對帳單"""
    statement = db.query(DBStatement).filter(DBStatement.id == statement_id).first()
    if not statement:
        raise HTTPException(status_code=404, detail="對帳單不存在")

    # 刪除相關項目
    db.query(DBStatementItem).filter(DBStatementItem.statement_id == statement_id).delete()
    
    # 刪除對帳單
    db.delete(statement)
    db.commit()
    
    return {"message": "對帳單已刪除"}

@router.get("/customers/list")
async def get_customers_for_statement(db: Session = Depends(get_db)):
    """取得可用於對帳單的客戶列表"""
    customers = db.query(DBCustomer).filter(DBCustomer.is_active == True).all()
    return [
        {
            "id": customer.id,
            "customer_code": customer.customer_code,
            "company_name": customer.company_name,
            "tax_id": customer.tax_id,
            "address": customer.address,
            "phone": customer.phone
        }
        for customer in customers
    ]
