from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel, field_validator
from typing import List, Optional, Union
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, AuxiliaryPricing as DBAuxiliaryPricing, Supplier as DBSupplier
import math

router = APIRouter()

# Pydantic 模型
class AuxiliaryPricingBase(BaseModel):
    date: Optional[str] = None
    supplier_code: Optional[str] = None
    product_name_code: Optional[str] = None
    specifications: Optional[str] = None
    unit: Optional[str] = None
    unit_price: Optional[float] = None
    product_category: Optional[str] = None
    currency: str = "VND"
    status: str = "active"
    effective_date: Optional[str] = None
    expiry_date: Optional[str] = None
    minimum_order_quantity: Optional[int] = None
    notes: Optional[str] = None
    created_by: Optional[str] = None

class AuxiliaryPricingCreate(AuxiliaryPricingBase):
    pass

class AuxiliaryPricingUpdate(BaseModel):
    date: Optional[str] = None
    supplier_code: Optional[str] = None
    product_name_code: Optional[str] = None
    specifications: Optional[str] = None
    unit: Optional[str] = None
    unit_price: Optional[float] = None
    product_category: Optional[str] = None
    currency: Optional[str] = None
    status: Optional[str] = None
    effective_date: Optional[str] = None
    expiry_date: Optional[str] = None
    minimum_order_quantity: Optional[int] = None
    notes: Optional[str] = None

class AuxiliaryPricing(AuxiliaryPricingBase):
    id: int
    created_at: datetime
    updated_at: datetime

    @field_validator('date', 'effective_date', 'expiry_date', mode='before')
    @classmethod
    def serialize_date(cls, v):
        if v is None:
            return None
        if isinstance(v, date):
            return v.strftime('%Y-%m-%d')
        return v

    class Config:
        from_attributes = True

class PaginatedAuxiliaryPricingResponse(BaseModel):
    data: List[AuxiliaryPricing]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedAuxiliaryPricingResponse)
@router.get("", response_model=PaginatedAuxiliaryPricingResponse)
async def get_auxiliary_pricing(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    supplier_code: Optional[str] = None,
    product_category: Optional[str] = None,
    unit: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """取得輔料單價列表"""
    query = db.query(DBAuxiliaryPricing)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBAuxiliaryPricing.supplier_code.contains(search)) |
            (DBAuxiliaryPricing.product_name_code.contains(search)) |
            (DBAuxiliaryPricing.specifications.contains(search))
        )
    
    if supplier_code:
        query = query.filter(DBAuxiliaryPricing.supplier_code.contains(supplier_code))
    
    if product_category:
        query = query.filter(DBAuxiliaryPricing.product_category.contains(product_category))
        
    if unit:
        query = query.filter(DBAuxiliaryPricing.unit == unit)
        
    if status:
        query = query.filter(DBAuxiliaryPricing.status == status)
        
    if date_from:
        query = query.filter(DBAuxiliaryPricing.date >= date_from)
        
    if date_to:
        query = query.filter(DBAuxiliaryPricing.date <= date_to)

    # 計算總數
    total = query.count()
    
    # 分頁 - 按日期倒序排列
    offset = (page - 1) * pageSize
    pricing_records = query.order_by(DBAuxiliaryPricing.date.desc()).offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedAuxiliaryPricingResponse(
        data=pricing_records,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{pricing_id}", response_model=AuxiliaryPricing)
@router.get("/{pricing_id}/", response_model=AuxiliaryPricing)
async def get_auxiliary_pricing_item(pricing_id: int, db: Session = Depends(get_db)):
    """取得單一輔料單價資料"""
    pricing = db.query(DBAuxiliaryPricing).filter(DBAuxiliaryPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="輔料單價不存在")
    return pricing

@router.post("/", response_model=AuxiliaryPricing, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=AuxiliaryPricing, status_code=status.HTTP_201_CREATED)
async def create_auxiliary_pricing(pricing: AuxiliaryPricingCreate, db: Session = Depends(get_db)):
    """建立輔料單價"""
    # 轉換日期字串為 date 物件
    pricing_data = pricing.model_dump()

    # 處理日期欄位
    for field in ['date', 'effective_date', 'expiry_date']:
        if pricing_data.get(field):
            try:
                pricing_data[field] = datetime.strptime(pricing_data[field], '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(status_code=422, detail=f'{field} 日期格式錯誤，請使用 YYYY-MM-DD 格式')

    # 創建新輔料單價
    db_pricing = DBAuxiliaryPricing(**pricing_data)
    db.add(db_pricing)
    db.commit()
    db.refresh(db_pricing)
    return db_pricing

@router.put("/{pricing_id}", response_model=AuxiliaryPricing)
@router.put("/{pricing_id}/", response_model=AuxiliaryPricing)
async def update_auxiliary_pricing(pricing_id: int, pricing_update: AuxiliaryPricingUpdate, db: Session = Depends(get_db)):
    """更新輔料單價"""
    pricing = db.query(DBAuxiliaryPricing).filter(DBAuxiliaryPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="輔料單價不存在")

    # 更新欄位
    update_data = pricing_update.model_dump(exclude_unset=True)

    # 處理日期欄位
    for field in ['date', 'effective_date', 'expiry_date']:
        if field in update_data and update_data[field]:
            try:
                update_data[field] = datetime.strptime(update_data[field], '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(status_code=422, detail=f'{field} 日期格式錯誤，請使用 YYYY-MM-DD 格式')

    for field, value in update_data.items():
        setattr(pricing, field, value)

    db.commit()
    db.refresh(pricing)
    return pricing

@router.delete("/{pricing_id}")
@router.delete("/{pricing_id}/")
async def delete_auxiliary_pricing(pricing_id: int, db: Session = Depends(get_db)):
    """刪除輔料單價"""
    pricing = db.query(DBAuxiliaryPricing).filter(DBAuxiliaryPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="輔料單價不存在")

    db.delete(pricing)
    db.commit()
    
    return {"message": "輔料單價已刪除"}

@router.get("/suppliers/list")
async def get_suppliers_list(db: Session = Depends(get_db)):
    """取得所有供應商代碼列表"""
    # 從供應商管理表中獲取活躍的供應商，特別是輔料供應商
    suppliers = db.query(DBSupplier).filter(
        DBSupplier.is_active == True,
        DBSupplier.supplier_type.in_(["輔料供應商", "紙板供應商", "紙箱廠(同行)"])
    ).all()

    return [{
        "supplier_code": supplier.supplier_code,
        "company_name": supplier.company_name,
        "supplier_type": supplier.supplier_type,
        "display_name": f"{supplier.supplier_code} - {supplier.company_name}"
    } for supplier in suppliers]

@router.get("/categories/list")
async def get_categories_list(db: Session = Depends(get_db)):
    """取得所有產品分類列表"""
    # 預設的商品分類
    default_categories = [
        "膠帶類",
        "包裝材料",
        "印刷耗材",
        "機械配件",
        "辦公用品",
        "清潔用品",
        "安全防護",
        "其他輔料"
    ]

    # 從資料庫中獲取已使用的分類
    db_categories = db.query(DBAuxiliaryPricing.product_category).distinct().filter(
        DBAuxiliaryPricing.product_category.isnot(None)
    ).all()
    db_category_list = [c.product_category for c in db_categories if c.product_category]

    # 合併預設分類和資料庫中的分類，去重
    all_categories = list(set(default_categories + db_category_list))
    all_categories.sort()  # 排序

    return all_categories

@router.get("/units/list")
async def get_units_list(db: Session = Depends(get_db)):
    """取得所有單位列表"""
    # 預設的單位選項
    default_units = [
        "PCS",
        "KG",
        "M",
        "M²",
        "SET",
        "BOX",
        "L",
        "cuộn(卷)"
    ]

    # 從資料庫中獲取已使用的單位
    db_units = db.query(DBAuxiliaryPricing.unit).distinct().filter(
        DBAuxiliaryPricing.unit.isnot(None)
    ).all()
    db_unit_list = [u.unit for u in db_units if u.unit]

    # 合併預設單位和資料庫中的單位，去重
    all_units = list(set(default_units + db_unit_list))

    # 按照預設順序排序，其他的放在後面
    sorted_units = []
    for unit in default_units:
        if unit in all_units:
            sorted_units.append(unit)

    # 添加其他不在預設列表中的單位
    for unit in all_units:
        if unit not in sorted_units:
            sorted_units.append(unit)

    return sorted_units

@router.get("/dashboard/summary")
async def get_pricing_summary(db: Session = Depends(get_db)):
    """取得輔料單價概況統計"""
    total = db.query(DBAuxiliaryPricing).count()
    active = db.query(DBAuxiliaryPricing).filter(DBAuxiliaryPricing.status == "active").count()
    inactive = db.query(DBAuxiliaryPricing).filter(DBAuxiliaryPricing.status == "inactive").count()
    
    # 供應商數量
    supplier_count = db.query(DBAuxiliaryPricing.supplier_code).distinct().count()
    
    # 產品分類數量
    category_count = db.query(DBAuxiliaryPricing.product_category).distinct().count()
    
    # 平均價格
    avg_price = db.query(DBAuxiliaryPricing.unit_price).filter(DBAuxiliaryPricing.status == "active").all()
    average_price = sum([price[0] for price in avg_price if price[0]]) / len(avg_price) if avg_price else 0
    
    return {
        "total_records": total,
        "active_records": active,
        "inactive_records": inactive,
        "supplier_count": supplier_count,
        "category_count": category_count,
        "average_price": round(average_price, 2)
    }

@router.post("/batch-update-status")
async def batch_update_status(pricing_ids: List[int], new_status: str, db: Session = Depends(get_db)):
    """批量更新狀態"""
    try:
        updated_count = db.query(DBAuxiliaryPricing).filter(
            DBAuxiliaryPricing.id.in_(pricing_ids)
        ).update({"status": new_status}, synchronize_session=False)
        
        db.commit()
        return {"message": f"已更新 {updated_count} 筆記錄的狀態為 {new_status}"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"批量更新失敗: {str(e)}")

@router.get("/search/by-product")
async def search_by_product(
    product_name_code: str,
    supplier_code: Optional[str] = None,
    unit: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """根據產品代號搜尋價格"""
    query = db.query(DBAuxiliaryPricing).filter(
        DBAuxiliaryPricing.product_name_code.contains(product_name_code),
        DBAuxiliaryPricing.status == "active"
    )
    
    if supplier_code:
        query = query.filter(DBAuxiliaryPricing.supplier_code == supplier_code)
        
    if unit:
        query = query.filter(DBAuxiliaryPricing.unit == unit)
    
    results = query.order_by(DBAuxiliaryPricing.date.desc()).all()
    return results

@router.get("/price-comparison/{product_name_code}")
async def get_price_comparison(product_name_code: str, db: Session = Depends(get_db)):
    """取得產品價格比較"""
    prices = db.query(DBAuxiliaryPricing).filter(
        DBAuxiliaryPricing.product_name_code == product_name_code,
        DBAuxiliaryPricing.status == "active"
    ).order_by(DBAuxiliaryPricing.unit_price.asc()).all()
    
    return {
        "product_name_code": product_name_code,
        "price_count": len(prices),
        "min_price": min([p.unit_price for p in prices]) if prices else 0,
        "max_price": max([p.unit_price for p in prices]) if prices else 0,
        "avg_price": sum([p.unit_price for p in prices]) / len(prices) if prices else 0,
        "prices": prices
    }
