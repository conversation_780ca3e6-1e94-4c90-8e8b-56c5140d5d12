from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from enum import Enum
from sqlalchemy.orm import Session
from database import get_db, MaterialOrder as DBMaterialOrder, MaterialOrderItem as DBMaterialOrderItem

router = APIRouter()

class OrderStatus(str, Enum):
    PENDING = "pending"
    ORDERED = "ordered"
    RECEIVED = "received"
    CANCELLED = "cancelled"

class MaterialOrderItem(BaseModel):
    material_id: int
    material_name: str
    specification: str
    quantity: float
    unit: str
    unit_price: float
    total_price: float

class MaterialOrderCreate(BaseModel):
    supplier_id: int
    supplier_name: str
    order_date: datetime
    expected_delivery_date: datetime
    items: List[MaterialOrderItem]
    notes: Optional[str] = None

class MaterialOrderResponse(BaseModel):
    id: int
    supplier_id: int
    supplier_name: str
    order_date: datetime
    expected_delivery_date: datetime
    actual_delivery_date: Optional[datetime] = None
    status: OrderStatus
    total_amount: float
    items: List[MaterialOrderItem]
    notes: Optional[str] = None

@router.get("/", response_model=List[MaterialOrderResponse])
async def get_material_orders(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[OrderStatus] = None,
    supplier_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """取得原物料訂單列表"""
    query = db.query(DBMaterialOrder)

    # 應用篩選條件
    if status:
        query = query.filter(DBMaterialOrder.status == status.value)
    if supplier_id:
        query = query.filter(DBMaterialOrder.supplier == str(supplier_id))

    # 分頁
    orders = query.offset(skip).limit(limit).all()

    # 轉換為響應格式
    result = []
    for order in orders:
        # 獲取訂單項目
        order_items = db.query(DBMaterialOrderItem).filter(
            DBMaterialOrderItem.order_id == order.id
        ).all()

        items = []
        for item in order_items:
            items.append({
                "material_id": item.material_id,
                "material_name": f"物料-{item.material_id}",
                "specification": item.notes or "",
                "quantity": item.quantity,
                "unit": "個",
                "unit_price": item.unit_price,
                "total_price": item.total_price
            })

        result.append({
            "id": order.id,
            "supplier_id": 1,
            "supplier_name": order.supplier,
            "order_date": order.order_date,
            "expected_delivery_date": order.expected_date,
            "actual_delivery_date": None,
            "status": order.status,
            "total_amount": order.total_amount,
            "items": items,
            "notes": order.notes
        })

    return result

@router.post("/", response_model=MaterialOrderResponse)
async def create_material_order(order: MaterialOrderCreate, db: Session = Depends(get_db)):
    """建立原物料訂單"""
    # 計算總金額
    total_amount = sum(item.total_price for item in order.items)

    # 生成訂單編號
    order_count = db.query(DBMaterialOrder).count()
    order_no = f"MAT-{order_count + 1:06d}"

    # 創建訂單
    db_order = DBMaterialOrder(
        order_no=order_no,
        supplier=order.supplier_name,
        order_date=order.order_date,
        expected_date=order.expected_delivery_date,
        status=OrderStatus.PENDING.value,
        total_amount=total_amount,
        notes=order.notes
    )

    db.add(db_order)
    db.commit()
    db.refresh(db_order)

    # 創建訂單項目
    for item in order.items:
        db_item = DBMaterialOrderItem(
            order_id=db_order.id,
            material_id=item.material_id,
            quantity=item.quantity,
            unit_price=item.unit_price,
            total_price=item.total_price,
            notes=item.specification
        )
        db.add(db_item)

    db.commit()

    # 返回創建的訂單
    return await get_material_order(db_order.id, db)

@router.get("/{order_id}", response_model=MaterialOrderResponse)
async def get_material_order(order_id: int, db: Session = Depends(get_db)):
    """取得原物料訂單詳情"""
    order = db.query(DBMaterialOrder).filter(DBMaterialOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 獲取訂單項目
    order_items = db.query(DBMaterialOrderItem).filter(
        DBMaterialOrderItem.order_id == order.id
    ).all()

    items = []
    for item in order_items:
        items.append({
            "material_id": item.material_id,
            "material_name": f"物料-{item.material_id}",
            "specification": item.notes or "",
            "quantity": item.quantity,
            "unit": "個",
            "unit_price": item.unit_price,
            "total_price": item.total_price
        })

    return {
        "id": order.id,
        "supplier_id": 1,
        "supplier_name": order.supplier,
        "order_date": order.order_date,
        "expected_delivery_date": order.expected_date,
        "actual_delivery_date": None,
        "status": order.status,
        "total_amount": order.total_amount,
        "items": items,
        "notes": order.notes
    }

@router.put("/{order_id}", response_model=MaterialOrderResponse)
async def update_material_order(order_id: int, order_update: MaterialOrderCreate, db: Session = Depends(get_db)):
    """更新原物料訂單"""
    order = db.query(DBMaterialOrder).filter(DBMaterialOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 更新訂單基本信息
    order.supplier = order_update.supplier_name
    order.order_date = order_update.order_date
    order.expected_date = order_update.expected_delivery_date
    order.notes = order_update.notes

    # 重新計算總金額
    total_amount = sum(item.total_price for item in order_update.items)
    order.total_amount = total_amount

    # 刪除舊的訂單項目
    db.query(DBMaterialOrderItem).filter(DBMaterialOrderItem.order_id == order_id).delete()

    # 添加新的訂單項目
    for item in order_update.items:
        db_item = DBMaterialOrderItem(
            order_id=order.id,
            material_id=item.material_id,
            quantity=item.quantity,
            unit_price=item.unit_price,
            total_price=item.total_price,
            notes=item.specification
        )
        db.add(db_item)

    db.commit()
    db.refresh(order)

    # 返回更新後的訂單
    return await get_material_order(order_id, db)

@router.delete("/{order_id}")
async def delete_material_order(order_id: int, db: Session = Depends(get_db)):
    """刪除原物料訂單"""
    order = db.query(DBMaterialOrder).filter(DBMaterialOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 刪除訂單項目
    db.query(DBMaterialOrderItem).filter(DBMaterialOrderItem.order_id == order_id).delete()

    # 刪除訂單
    db.delete(order)
    db.commit()

    return {"message": "訂單已刪除"}

@router.put("/{order_id}/status")
async def update_order_status(order_id: int, status: OrderStatus):
    """更新訂單狀態"""
    # TODO: 實際更新資料庫
    return {"message": f"訂單 {order_id} 狀態已更新"}

@router.get("/suppliers")
async def get_suppliers():
    """取得供應商列表"""
    # TODO: 實際從資料庫查詢
    return [
        {"id": 1, "name": "紙業供應商A", "contact": "02-1234-5678"},
        {"id": 2, "name": "油墨供應商B", "contact": "02-2345-6789"}
    ]
