from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from database import (
    get_db, 
    Customer, 
    CustomerOrder, 
    CustomerOrderItem,
    Product,
    ProductionTask,
    Workstation,
    Employee,
    InventoryItem
)

router = APIRouter()

# 響應模型
class DashboardStats(BaseModel):
    total_orders: int
    total_revenue: float
    total_inventory_items: int
    total_employees: int
    monthly_growth: float
    pending_orders: int

class WorkstationStatus(BaseModel):
    name: str
    utilization: float
    status: str
    current_task: Optional[str] = None

class RecentOrder(BaseModel):
    id: int
    order_no: str
    customer: str
    amount: float
    status: str
    order_date: str

class SystemAlert(BaseModel):
    type: str
    message: str
    priority: int

class DashboardOverview(BaseModel):
    stats: DashboardStats
    workstation_status: List[WorkstationStatus]
    recent_orders: List[RecentOrder]
    system_alerts: List[SystemAlert]

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """獲取儀表板統計數據"""
    # 計算總訂單數
    total_orders = db.query(CustomerOrder).count()
    
    # 計算總收入
    total_revenue_result = db.query(func.sum(CustomerOrder.total_amount)).scalar()
    total_revenue = float(total_revenue_result or 0)
    
    # 計算庫存項目數
    total_inventory_items = db.query(InventoryItem).count()
    
    # 計算員工總數
    total_employees = db.query(Employee).filter(Employee.is_active == True).count()
    
    # 計算月度增長率（與上個月比較）
    current_month_start = date.today().replace(day=1)
    last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
    
    current_month_revenue = db.query(func.sum(CustomerOrder.total_amount)).filter(
        CustomerOrder.order_date >= current_month_start
    ).scalar() or 0
    
    last_month_revenue = db.query(func.sum(CustomerOrder.total_amount)).filter(
        and_(
            CustomerOrder.order_date >= last_month_start,
            CustomerOrder.order_date < current_month_start
        )
    ).scalar() or 0
    
    monthly_growth = 0
    if last_month_revenue > 0:
        monthly_growth = ((current_month_revenue - last_month_revenue) / last_month_revenue) * 100
    
    # 計算待處理訂單數
    pending_orders = db.query(CustomerOrder).filter(
        CustomerOrder.status.in_(["pending", "confirmed"])
    ).count()
    
    return DashboardStats(
        total_orders=total_orders,
        total_revenue=total_revenue,
        total_inventory_items=total_inventory_items,
        total_employees=total_employees,
        monthly_growth=round(monthly_growth, 2),
        pending_orders=pending_orders
    )

@router.get("/workstation-status", response_model=List[WorkstationStatus])
async def get_workstation_status(db: Session = Depends(get_db)):
    """獲取工作站狀態"""
    workstations = db.query(Workstation).filter(Workstation.is_active == True).all()
    
    status_list = []
    for ws in workstations:
        # 計算使用率
        today_tasks = db.query(ProductionTask).filter(
            and_(
                ProductionTask.workstation == ws.code,
                ProductionTask.scheduled_date == date.today(),
                ProductionTask.status.in_(["pending", "in_progress"])
            )
        ).all()
        
        total_estimated_hours = sum(task.estimated_hours for task in today_tasks)
        utilization_rate = min((total_estimated_hours / ws.daily_capacity_hours) * 100, 100)
        
        # 獲取當前任務
        current_task = db.query(ProductionTask).filter(
            and_(
                ProductionTask.workstation == ws.code,
                ProductionTask.status == "in_progress"
            )
        ).first()
        
        current_task_name = None
        if current_task:
            current_task_name = f"{current_task.customer_name} - {current_task.product_name}"
        
        status_list.append(WorkstationStatus(
            name=ws.name,
            utilization=round(utilization_rate, 1),
            status=ws.status,
            current_task=current_task_name
        ))
    
    return status_list

@router.get("/recent-orders", response_model=List[RecentOrder])
async def get_recent_orders(limit: int = Query(10, ge=1, le=50), db: Session = Depends(get_db)):
    """獲取最近訂單"""
    orders = db.query(CustomerOrder).join(
        Customer, CustomerOrder.customer_id == Customer.id
    ).order_by(desc(CustomerOrder.created_at)).limit(limit).all()
    
    recent_orders = []
    for order in orders:
        recent_orders.append(RecentOrder(
            id=order.id,
            order_no=order.order_no,
            customer=order.customer.company_name,
            amount=order.total_amount,
            status=order.status,
            order_date=order.order_date.strftime('%Y-%m-%d')
        ))
    
    return recent_orders

@router.get("/system-alerts", response_model=List[SystemAlert])
async def get_system_alerts(db: Session = Depends(get_db)):
    """獲取系統警報"""
    alerts = []
    
    # 檢查逾期任務
    overdue_tasks = db.query(ProductionTask).filter(
        and_(
            ProductionTask.scheduled_date < date.today(),
            ProductionTask.status.in_(["pending", "in_progress"])
        )
    ).count()
    
    if overdue_tasks > 0:
        alerts.append(SystemAlert(
            type="warning",
            message=f"有 {overdue_tasks} 個生產任務逾期",
            priority=2
        ))
    
    # 檢查低庫存
    low_stock_items = db.query(InventoryItem).filter(
        InventoryItem.current_stock < InventoryItem.min_stock
    ).count()
    
    if low_stock_items > 0:
        alerts.append(SystemAlert(
            type="error",
            message=f"有 {low_stock_items} 個庫存項目低於最低庫存",
            priority=1
        ))
    
    # 檢查待處理訂單
    pending_orders = db.query(CustomerOrder).filter(
        CustomerOrder.status == "pending"
    ).count()
    
    if pending_orders > 10:
        alerts.append(SystemAlert(
            type="info",
            message=f"有 {pending_orders} 個訂單待處理",
            priority=3
        ))
    
    # 檢查工作站維護
    maintenance_workstations = db.query(Workstation).filter(
        Workstation.status == "maintenance"
    ).count()
    
    if maintenance_workstations > 0:
        alerts.append(SystemAlert(
            type="warning",
            message=f"有 {maintenance_workstations} 個工作站正在維護中",
            priority=2
        ))
    
    # 按優先級排序
    alerts.sort(key=lambda x: x.priority)
    
    return alerts

@router.get("/overview", response_model=DashboardOverview)
async def get_dashboard_overview(db: Session = Depends(get_db)):
    """獲取儀表板總覽數據"""
    stats = await get_dashboard_stats(db)
    workstation_status = await get_workstation_status(db)
    recent_orders = await get_recent_orders(5, db)
    system_alerts = await get_system_alerts(db)
    
    return DashboardOverview(
        stats=stats,
        workstation_status=workstation_status,
        recent_orders=recent_orders,
        system_alerts=system_alerts
    )
