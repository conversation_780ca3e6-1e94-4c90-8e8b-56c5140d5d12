from fastapi import APIRouter, HTTPException, Query, Path, Depends
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from enum import Enum
from sqlalchemy.orm import Session
from database import get_db, CustomerOrder as DBCustomerOrder, CustomerOrderItem as DBCustomerOrderItem, Customer as DBCustomer, Product as DBProduct
import math

router = APIRouter()

# 訂單狀態枚舉
class OrderStatus(str, Enum):
    PENDING = "pending"          # 待處理
    CONFIRMED = "confirmed"      # 已確認
    PRODUCTION = "production"    # 生產中
    COMPLETED = "completed"      # 已完成
    CANCELLED = "cancelled"      # 已取消

# 資料模型
class CustomerOrderItem(BaseModel):
    product_id: Optional[int] = None
    product_name: str
    quantity: Optional[int] = 0
    unit_price: Optional[float] = 0.0
    total_price: Optional[float] = 0.0
    specifications: Optional[str] = None

class CustomerOrderCreate(BaseModel):
    customer_id: int
    customer_name: str
    order_date: datetime
    delivery_date: datetime
    items: List[CustomerOrderItem]
    notes: Optional[str] = None

class CustomerOrderUpdate(BaseModel):
    customer_name: Optional[str] = None
    delivery_date: Optional[datetime] = None
    status: Optional[OrderStatus] = None
    items: Optional[List[CustomerOrderItem]] = None
    notes: Optional[str] = None

class CustomerOrderResponse(BaseModel):
    id: int
    customer_id: int
    customer_name: str
    order_date: datetime
    delivery_date: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    total_amount: Optional[float] = 0.0
    items: List[CustomerOrderItem] = []
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

class OrderProgress(BaseModel):
    order_id: int
    current_stage: str
    progress_percentage: int
    estimated_completion: datetime
    production_notes: Optional[str] = None

class PaginatedOrderResponse(BaseModel):
    data: List[CustomerOrderResponse]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedOrderResponse)
async def get_customer_orders(
    page: int = Query(1, ge=1),
    pageSize: int = Query(50, ge=1, le=100),
    status: Optional[OrderStatus] = None,
    customer_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    """取得客戶訂單列表"""
    query = db.query(DBCustomerOrder).join(DBCustomer)

    # 應用篩選條件
    if status:
        query = query.filter(DBCustomerOrder.status == status.value)
    if customer_id:
        query = query.filter(DBCustomerOrder.customer_id == customer_id)
    if start_date:
        query = query.filter(DBCustomerOrder.order_date >= start_date)
    if end_date:
        query = query.filter(DBCustomerOrder.order_date <= end_date)

    # 計算總筆數
    total = query.count()

    # 計算分頁參數
    skip = (page - 1) * pageSize
    totalPages = math.ceil(total / pageSize) if total > 0 else 0

    # 查詢分頁資料
    orders = query.offset(skip).limit(pageSize).all()

    # 轉換為響應格式
    result = []
    for order in orders:
        # 獲取訂單項目
        order_items = db.query(DBCustomerOrderItem).filter(
            DBCustomerOrderItem.order_id == order.id
        ).all()

        items = []
        for item in order_items:
            product = db.query(DBProduct).filter(DBProduct.id == item.product_id).first()
            items.append({
                "product_id": item.product_id,
                "product_name": product.product_name if product else "未知產品",
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "total_price": item.total_price,
                "specifications": item.notes
            })

        result.append({
            "id": order.id,
            "customer_id": order.customer_id,
            "customer_name": order.customer.company_name,
            "order_date": order.order_date,
            "delivery_date": order.delivery_date,
            "status": order.status,
            "total_amount": order.total_amount,
            "items": items,
            "notes": order.notes,
            "created_at": order.created_at,
            "updated_at": order.updated_at
        })

    return PaginatedOrderResponse(
        data=result,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=totalPages
    )

@router.post("/", response_model=CustomerOrderResponse)
async def create_customer_order(order: CustomerOrderCreate, db: Session = Depends(get_db)):
    """建立新的客戶訂單"""
    # 檢查客戶是否存在
    customer = db.query(DBCustomer).filter(DBCustomer.id == order.customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客戶不存在")

    # 計算總金額
    total_amount = sum(item.total_price for item in order.items)

    # 生成訂單編號
    order_count = db.query(DBCustomerOrder).count()
    order_no = f"ORD-{order_count + 1:06d}"

    # 創建訂單
    db_order = DBCustomerOrder(
        order_no=order_no,
        customer_id=order.customer_id,
        order_date=order.order_date,
        delivery_date=order.delivery_date,
        status=OrderStatus.PENDING.value,
        total_amount=total_amount,
        notes=order.notes
    )

    db.add(db_order)
    db.commit()
    db.refresh(db_order)

    # 創建訂單項目
    for item in order.items:
        db_item = DBCustomerOrderItem(
            order_id=db_order.id,
            product_id=item.product_id,
            quantity=item.quantity,
            unit_price=item.unit_price,
            total_price=item.total_price,
            notes=item.specifications
        )
        db.add(db_item)

    db.commit()

    # 返回創建的訂單
    return await get_customer_order(db_order.id, db)

@router.get("/{order_id}", response_model=CustomerOrderResponse)
async def get_customer_order(order_id: int = Path(..., gt=0), db: Session = Depends(get_db)):
    """取得特定客戶訂單詳情"""
    order = db.query(DBCustomerOrder).filter(DBCustomerOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 獲取客戶資訊
    customer = db.query(DBCustomer).filter(DBCustomer.id == order.customer_id).first()

    # 獲取訂單項目
    order_items = db.query(DBCustomerOrderItem).filter(
        DBCustomerOrderItem.order_id == order.id
    ).all()

    items = []
    for item in order_items:
        product = db.query(DBProduct).filter(DBProduct.id == item.product_id).first()
        items.append({
            "product_id": item.product_id,
            "product_name": product.product_name if product else "未知產品",
            "quantity": item.quantity,
            "unit_price": item.unit_price,
            "total_price": item.total_price,
            "specifications": item.notes
        })

    return {
        "id": order.id,
        "customer_id": order.customer_id,
        "customer_name": customer.company_name if customer else "未知客戶",
        "order_date": order.order_date,
        "delivery_date": order.delivery_date,
        "status": order.status,
        "total_amount": order.total_amount,
        "items": items,
        "notes": order.notes,
        "created_at": order.created_at,
        "updated_at": order.updated_at
    }

@router.put("/{order_id}", response_model=CustomerOrderResponse)
async def update_customer_order(
    order_id: int = Path(..., gt=0),
    order_update: CustomerOrderCreate = None,
    db: Session = Depends(get_db)
):
    """更新客戶訂單"""
    order = db.query(DBCustomerOrder).filter(DBCustomerOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 更新訂單基本信息
    if order_update:
        order.customer_id = order_update.customer_id
        order.order_date = order_update.order_date
        order.delivery_date = order_update.delivery_date
        order.status = order_update.status if hasattr(order_update, 'status') else order.status
        order.notes = order_update.notes

        # 重新計算總金額
        total_amount = sum(item.total_price for item in order_update.items)
        order.total_amount = total_amount

        # 刪除舊的訂單項目
        db.query(DBCustomerOrderItem).filter(DBCustomerOrderItem.order_id == order_id).delete()

        # 添加新的訂單項目
        for item in order_update.items:
            db_item = DBCustomerOrderItem(
                order_id=order.id,
                product_id=item.product_id,
                quantity=item.quantity,
                unit_price=item.unit_price,
                total_price=item.total_price,
                notes=item.specifications
            )
            db.add(db_item)

    db.commit()
    db.refresh(order)

    # 返回更新後的訂單
    return await get_customer_order(order_id, db)

@router.delete("/{order_id}")
async def delete_customer_order(
    order_id: int = Path(..., gt=0),
    db: Session = Depends(get_db)
):
    """刪除客戶訂單"""
    order = db.query(DBCustomerOrder).filter(DBCustomerOrder.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="訂單不存在")

    # 刪除訂單項目
    db.query(DBCustomerOrderItem).filter(DBCustomerOrderItem.order_id == order_id).delete()

    # 刪除訂單
    db.delete(order)
    db.commit()

    return {"message": "訂單已刪除"}



@router.get("/{order_id}/progress", response_model=OrderProgress)
async def get_order_progress(order_id: int):
    """取得訂單進度追蹤"""
    # TODO: 實際從資料庫查詢
    return {
        "order_id": order_id,
        "current_stage": "印刷機1",
        "progress_percentage": 65,
        "estimated_completion": datetime.now(),
        "production_notes": "印刷進行中，預計明天完成"
    }

@router.put("/{order_id}/status")
async def update_order_status(order_id: int, status: OrderStatus):
    """更新訂單狀態"""
    # TODO: 實際更新資料庫
    return {"message": f"訂單 {order_id} 狀態已更新為 {status.value}"}
