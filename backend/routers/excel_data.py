from fastapi import APIRouter, Query, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import sqlite3
import math

router = APIRouter()

class ProductQuotation(BaseModel):
    id: int
    quoteDate: Optional[str] = None
    supplier: Optional[str] = None
    customerName: Optional[str] = None
    productCode: str
    moldNumber: Optional[str] = None
    category: Optional[str] = None
    finishedSize: Optional[str] = None
    materialWeight: Optional[str] = None
    materialCode: Optional[str] = None
    paperSize: Optional[str] = None
    doorWidth: Optional[float] = None
    cutWidth: Optional[float] = None
    cutLength: Optional[float] = None
    smallSheets: Optional[int] = None
    largeSheets: Optional[int] = None
    cuttingCount: Optional[int] = None
    corrugatedType: Optional[str] = None
    pressingLine: Optional[str] = None
    meters: Optional[float] = None
    areaSqm: Optional[float] = None
    paperUnitPrice: Optional[float] = None
    subtotalBeforeTax: Optional[float] = None
    moq: Optional[float] = None
    laborCost: Optional[float] = None
    transportFee: Optional[float] = None
    moldFee: Optional[float] = None
    additionalCost: Optional[float] = None
    totalCost: Optional[float] = None
    unitPrice: Optional[float] = None
    profitAmount: Optional[float] = None
    profitMargin: Optional[float] = None

class OrderManagement(BaseModel):
    id: int
    orderNumber: str
    customerCode: Optional[str] = None
    companyFullName: Optional[str] = None
    orderDate: Optional[str] = None
    deliveryDate: Optional[str] = None
    productName: Optional[str] = None
    specifications: Optional[str] = None
    unit: Optional[str] = None
    orderQuantity: Optional[int] = None
    paperOrderQuantity: Optional[int] = None
    actualPaperQuantity: Optional[int] = None
    printingPlate: Optional[str] = None
    moldNumber: Optional[str] = None
    productionStatus: Optional[str] = None
    unitPrice: Optional[float] = None
    totalAmount: Optional[float] = None
    deliveryQuantity: Optional[int] = None
    remainingQuantity: Optional[int] = None
    deliveryNoteNumber: Optional[str] = None
    notes: Optional[str] = None

class PaginatedQuotationResponse(BaseModel):
    data: List[ProductQuotation]
    total: int
    page: int
    pageSize: int
    totalPages: int

class PaginatedOrderResponse(BaseModel):
    data: List[OrderManagement]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/product-quotations", response_model=PaginatedQuotationResponse)
async def get_product_quotations(
    page: int = Query(1, ge=1),
    pageSize: int = Query(50, ge=1, le=100),
    category: Optional[str] = None,
    customer: Optional[str] = None,
    search: Optional[str] = None
):
    """取得商品報價列表 - Excel 格式"""
    conn = sqlite3.connect('db/erp.db')
    cursor = conn.cursor()

    try:
        # 構建查詢條件
        where_conditions = []
        params = []

        if category:
            where_conditions.append("category = ?")
            params.append(category)

        if customer:
            where_conditions.append("product_name LIKE ?")
            params.append(f"%{customer}%")

        if search:
            where_conditions.append("(product_code LIKE ? OR product_name LIKE ? OR finished_size LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 計算總數
        count_query = f"SELECT COUNT(*) FROM products{where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # 計算分頁
        offset = (page - 1) * pageSize
        total_pages = math.ceil(total / pageSize)

        # 查詢資料 - 優先顯示有完整資料的產品
        query = f"""
        SELECT
            id, product_code, product_name, category, finished_size,
            material_weight, material_code, board_size, width_cut, length_cut,
            small_sheets, large_sheets, cutting_sheets, corrugated, pressing_line,
            meters, area, board_unit_price, subtotal, moq,
            labor_cost, transport_cost, mold_cost, other_cost, total_cost,
            selling_price, profit_amount, profit_rate, created_at
        FROM products{where_clause}
        ORDER BY
            CASE
                WHEN width_cut IS NOT NULL AND length_cut IS NOT NULL AND selling_price IS NOT NULL THEN 0
                WHEN width_cut IS NOT NULL OR length_cut IS NOT NULL OR selling_price IS NOT NULL THEN 1
                ELSE 2
            END,
            id DESC
        LIMIT ? OFFSET ?
        """
        
        cursor.execute(query, params + [pageSize, offset])
        rows = cursor.fetchall()

        # 轉換為 ProductQuotation 格式
        products = []
        for row in rows:
            product = ProductQuotation(
                id=row[0],
                productCode=row[1] or "",
                customerName="",  # 從產品名稱中提取客戶信息
                category=row[3] or "",
                finishedSize=row[4] or "",
                materialWeight=row[5] or "",
                materialCode=row[6] or "",
                paperSize=row[7] or "",
                cutWidth=row[8],
                cutLength=row[9],
                smallSheets=row[10],
                largeSheets=row[11],
                cuttingCount=row[12],
                corrugatedType=row[13] or "",
                pressingLine=row[14] or "",
                meters=row[15],
                areaSqm=row[16],
                paperUnitPrice=row[17],
                subtotalBeforeTax=row[18],
                moq=row[19],
                laborCost=row[20],
                transportFee=row[21],
                moldFee=row[22],
                additionalCost=row[23],
                totalCost=row[24],
                unitPrice=row[25],
                profitAmount=row[26],
                profitMargin=row[27],
                quoteDate=row[28][:10] if row[28] else None  # 只取日期部分
            )
            products.append(product)

        return PaginatedQuotationResponse(
            data=products,
            total=total,
            page=page,
            pageSize=pageSize,
            totalPages=total_pages
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"資料庫查詢錯誤: {str(e)}")
    finally:
        conn.close()

@router.get("/order-management", response_model=PaginatedOrderResponse)
async def get_order_management(
    page: int = Query(1, ge=1),
    pageSize: int = Query(50, ge=1, le=100),
    customer: Optional[str] = None,
    status: Optional[str] = None,
    search: Optional[str] = None
):
    """取得訂單管理列表 - Excel 格式"""
    conn = sqlite3.connect('db/erp.db')
    cursor = conn.cursor()

    try:
        # 構建查詢條件
        where_conditions = []
        params = []

        if customer:
            where_conditions.append("c.customer_code = ?")
            params.append(customer)

        if status:
            where_conditions.append("o.status = ?")
            params.append(status)

        if search:
            where_conditions.append("(o.order_no LIKE ? OR c.company_name LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 計算總數
        count_query = f"""
        SELECT COUNT(*) 
        FROM customer_orders o 
        LEFT JOIN customers c ON o.customer_id = c.id
        {where_clause}
        """
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # 計算分頁
        offset = (page - 1) * pageSize
        total_pages = math.ceil(total / pageSize)

        # 查詢資料 - 優先顯示有完整資料的訂單
        query = f"""
        SELECT
            o.id, o.order_no, c.customer_code, c.company_name,
            o.order_date, o.delivery_date, o.notes,
            o.status, o.total_amount, o.created_at
        FROM customer_orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        {where_clause}
        ORDER BY
            CASE
                WHEN o.order_no IS NOT NULL AND c.customer_code IS NOT NULL AND o.total_amount > 0 THEN 0
                WHEN o.order_no IS NOT NULL OR c.customer_code IS NOT NULL THEN 1
                ELSE 2
            END,
            o.order_date DESC, o.id DESC
        LIMIT ? OFFSET ?
        """
        
        cursor.execute(query, params + [pageSize, offset])
        rows = cursor.fetchall()

        # 轉換為 OrderManagement 格式
        orders = []
        for row in rows:
            order = OrderManagement(
                id=row[0],
                orderNumber=row[1] or "",
                customerCode=row[2] or "",
                companyFullName=row[3] or "",
                orderDate=row[4],
                deliveryDate=row[5],
                productName="",  # 需要從訂單明細中獲取
                specifications="",
                unit="PCS",
                orderQuantity=0,
                paperOrderQuantity=0,
                actualPaperQuantity=0,
                printingPlate="",
                moldNumber="",
                productionStatus=row[7] or "待生產",
                unitPrice=0.0,
                totalAmount=row[8],
                deliveryQuantity=0,
                remainingQuantity=0,
                deliveryNoteNumber="",
                notes=row[6] or ""
            )
            orders.append(order)

        return PaginatedOrderResponse(
            data=orders,
            total=total,
            page=page,
            pageSize=pageSize,
            totalPages=total_pages
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"資料庫查詢錯誤: {str(e)}")
    finally:
        conn.close()
