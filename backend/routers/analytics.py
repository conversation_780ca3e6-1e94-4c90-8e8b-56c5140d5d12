from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from database import (
    get_db,
    Customer,
    CustomerOrder,
    CustomerOrderItem,
    Product,
    ProductionTask,
    Workstation
)

router = APIRouter()

# 響應模型
class CustomerStats(BaseModel):
    total_sales: float
    total_orders: int
    avg_order_value: float
    active_customers: int

class SalesTrendData(BaseModel):
    date: str
    sales: float
    orders: int

class CustomerRankingData(BaseModel):
    customer_name: str
    total_sales: float
    order_count: int
    avg_order_value: float

class ProductSalesData(BaseModel):
    product_name: str
    sales: float
    quantity: float
    percentage: float

class ProductionAnalytics(BaseModel):
    workstation_utilization: List[Dict[str, Any]]
    task_completion_rate: float
    avg_completion_time: float
    overdue_tasks: int

@router.get("/customer-stats", response_model=CustomerStats)
async def get_customer_stats(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """獲取客戶統計數據"""
    # 計算總銷售額
    total_sales_result = db.query(func.sum(CustomerOrder.total_amount)).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).scalar()
    total_sales = float(total_sales_result or 0)
    
    # 計算總訂單數
    total_orders = db.query(CustomerOrder).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).count()
    
    # 計算平均訂單價值
    avg_order_value = total_sales / total_orders if total_orders > 0 else 0
    
    # 計算活躍客戶數（在指定期間內有訂單的客戶）
    active_customers = db.query(func.count(func.distinct(CustomerOrder.customer_id))).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).scalar()
    
    return CustomerStats(
        total_sales=total_sales,
        total_orders=total_orders,
        avg_order_value=avg_order_value,
        active_customers=active_customers or 0
    )

@router.get("/sales-trend", response_model=List[SalesTrendData])
async def get_sales_trend(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """獲取銷售趨勢數據"""
    # 按日期分組查詢銷售數據
    results = db.query(
        CustomerOrder.order_date,
        func.sum(CustomerOrder.total_amount).label('daily_sales'),
        func.count(CustomerOrder.id).label('daily_orders')
    ).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).group_by(CustomerOrder.order_date).order_by(CustomerOrder.order_date).all()
    
    trend_data = []
    for result in results:
        trend_data.append(SalesTrendData(
            date=result.order_date.strftime('%Y-%m-%d'),
            sales=float(result.daily_sales or 0),
            orders=result.daily_orders or 0
        ))
    
    return trend_data

@router.get("/customer-ranking", response_model=List[CustomerRankingData])
async def get_customer_ranking(
    start_date: date = Query(...),
    end_date: date = Query(...),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """獲取客戶排名數據"""
    results = db.query(
        Customer.company_name,
        func.sum(CustomerOrder.total_amount).label('total_sales'),
        func.count(CustomerOrder.id).label('order_count')
    ).join(
        CustomerOrder, Customer.id == CustomerOrder.customer_id
    ).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).group_by(
        Customer.id, Customer.company_name
    ).order_by(
        desc('total_sales')
    ).limit(limit).all()
    
    ranking_data = []
    for result in results:
        total_sales = float(result.total_sales or 0)
        order_count = result.order_count or 0
        avg_order_value = total_sales / order_count if order_count > 0 else 0
        
        ranking_data.append(CustomerRankingData(
            customer_name=result.company_name,
            total_sales=total_sales,
            order_count=order_count,
            avg_order_value=avg_order_value
        ))
    
    return ranking_data

@router.get("/product-sales", response_model=List[ProductSalesData])
async def get_product_sales(
    start_date: date = Query(...),
    end_date: date = Query(...),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """獲取產品銷售數據"""
    # 先計算總銷售額用於計算百分比
    total_sales_result = db.query(func.sum(CustomerOrderItem.total_price)).join(
        CustomerOrder, CustomerOrderItem.order_id == CustomerOrder.id
    ).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).scalar()
    total_sales = float(total_sales_result or 0)

    # 按產品分組查詢銷售數據
    results = db.query(
        CustomerOrderItem.product_name,
        func.sum(CustomerOrderItem.total_price).label('product_sales'),
        func.sum(CustomerOrderItem.quantity).label('total_quantity')
    ).join(
        CustomerOrder, CustomerOrderItem.order_id == CustomerOrder.id
    ).filter(
        and_(
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).group_by(
        CustomerOrderItem.product_name
    ).order_by(
        desc('product_sales')
    ).limit(limit).all()

    product_data = []
    for result in results:
        product_sales = float(result.product_sales or 0)
        percentage = (product_sales / total_sales * 100) if total_sales > 0 else 0

        product_data.append(ProductSalesData(
            product_name=result.product_name,
            sales=product_sales,
            quantity=result.total_quantity or 0,
            percentage=round(percentage, 2)
        ))

    return product_data

@router.get("/production-analytics", response_model=ProductionAnalytics)
async def get_production_analytics(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """獲取生產分析數據"""
    # 工作站使用率分析
    workstation_utilization = []
    workstations = db.query(Workstation).filter(Workstation.is_active == True).all()
    
    for ws in workstations:
        # 計算該工作站在指定期間的任務總時數
        total_hours_result = db.query(func.sum(ProductionTask.estimated_hours)).filter(
            and_(
                ProductionTask.workstation == ws.code,
                ProductionTask.scheduled_date >= start_date,
                ProductionTask.scheduled_date <= end_date
            )
        ).scalar()
        total_hours = float(total_hours_result or 0)
        
        # 計算期間內的工作日數
        work_days = (end_date - start_date).days + 1
        available_hours = work_days * ws.daily_capacity_hours
        
        utilization_rate = (total_hours / available_hours * 100) if available_hours > 0 else 0
        
        workstation_utilization.append({
            "workstation": ws.name,
            "utilization_rate": round(utilization_rate, 2),
            "total_hours": total_hours,
            "available_hours": available_hours
        })
    
    # 任務完成率
    total_tasks = db.query(ProductionTask).filter(
        and_(
            ProductionTask.scheduled_date >= start_date,
            ProductionTask.scheduled_date <= end_date
        )
    ).count()
    
    completed_tasks = db.query(ProductionTask).filter(
        and_(
            ProductionTask.scheduled_date >= start_date,
            ProductionTask.scheduled_date <= end_date,
            ProductionTask.status == "completed"
        )
    ).count()
    
    completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    # 平均完成時間（小時）
    avg_completion_result = db.query(func.avg(ProductionTask.actual_hours)).filter(
        and_(
            ProductionTask.scheduled_date >= start_date,
            ProductionTask.scheduled_date <= end_date,
            ProductionTask.status == "completed",
            ProductionTask.actual_hours > 0
        )
    ).scalar()
    avg_completion_time = float(avg_completion_result or 0)
    
    # 逾期任務數
    overdue_tasks = db.query(ProductionTask).filter(
        and_(
            ProductionTask.scheduled_date < date.today(),
            ProductionTask.status.in_(["pending", "in_progress"])
        )
    ).count()
    
    return ProductionAnalytics(
        workstation_utilization=workstation_utilization,
        task_completion_rate=round(completion_rate, 2),
        avg_completion_time=round(avg_completion_time, 2),
        overdue_tasks=overdue_tasks
    )
