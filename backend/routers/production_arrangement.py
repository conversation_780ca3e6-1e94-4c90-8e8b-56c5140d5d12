from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, ProductionArrangement as DBProductionArrangement
import math

router = APIRouter()

# Pydantic 模型
class ProductionArrangementBase(BaseModel):
    sequence_no: Optional[int] = None
    customer_name: Optional[str] = None
    order_code: Optional[str] = None
    material_code: Optional[str] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    product_name: Optional[str] = None
    product_name_with_spec: Optional[str] = None
    ordered_quantity: Optional[int] = None
    actual_quantity: Optional[int] = None
    notes: Optional[str] = None
    production_status: str = "pending"
    priority: str = "normal"
    scheduled_date: Optional[date] = None
    start_date: Optional[date] = None
    completion_date: Optional[date] = None
    created_by: Optional[str] = None

class ProductionArrangementCreate(ProductionArrangementBase):
    pass

class ProductionArrangementUpdate(BaseModel):
    sequence_no: Optional[int] = None
    customer_name: Optional[str] = None
    order_code: Optional[str] = None
    material_code: Optional[str] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    product_name: Optional[str] = None
    product_name_with_spec: Optional[str] = None
    ordered_quantity: Optional[int] = None
    actual_quantity: Optional[int] = None
    notes: Optional[str] = None
    production_status: Optional[str] = None
    priority: Optional[str] = None
    scheduled_date: Optional[date] = None
    start_date: Optional[date] = None
    completion_date: Optional[date] = None

class ProductionArrangement(ProductionArrangementBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaginatedProductionArrangementResponse(BaseModel):
    data: List[ProductionArrangement]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedProductionArrangementResponse)
@router.get("", response_model=PaginatedProductionArrangementResponse)
async def get_production_arrangements(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    production_status: Optional[str] = None,
    priority: Optional[str] = None,
    customer_name: Optional[str] = None,
    scheduled_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """取得生產安排列表"""
    query = db.query(DBProductionArrangement)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBProductionArrangement.order_code.contains(search)) |
            (DBProductionArrangement.product_name.contains(search)) |
            (DBProductionArrangement.customer_name.contains(search)) |
            (DBProductionArrangement.material_code.contains(search))
        )
    
    if production_status:
        query = query.filter(DBProductionArrangement.production_status == production_status)
    
    if priority:
        query = query.filter(DBProductionArrangement.priority == priority)
        
    if customer_name:
        query = query.filter(DBProductionArrangement.customer_name.contains(customer_name))
        
    if scheduled_date:
        query = query.filter(DBProductionArrangement.scheduled_date == scheduled_date)

    # 計算總數
    total = query.count()
    
    # 分頁 - 按序號排序
    offset = (page - 1) * pageSize
    arrangements = query.order_by(DBProductionArrangement.sequence_no.asc()).offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedProductionArrangementResponse(
        data=arrangements,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{arrangement_id}", response_model=ProductionArrangement)
@router.get("/{arrangement_id}/", response_model=ProductionArrangement)
async def get_production_arrangement(arrangement_id: int, db: Session = Depends(get_db)):
    """取得單一生產安排資料"""
    arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
    if not arrangement:
        raise HTTPException(status_code=404, detail="生產安排不存在")
    return arrangement

@router.post("/", response_model=ProductionArrangement, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=ProductionArrangement, status_code=status.HTTP_201_CREATED)
async def create_production_arrangement(arrangement: ProductionArrangementCreate, db: Session = Depends(get_db)):
    """建立生產安排"""
    # 如果沒有提供序號，自動生成
    if not arrangement.sequence_no:
        max_sequence = db.query(DBProductionArrangement.sequence_no).order_by(DBProductionArrangement.sequence_no.desc()).first()
        arrangement.sequence_no = (max_sequence[0] if max_sequence and max_sequence[0] else 0) + 1

    # 創建新生產安排
    db_arrangement = DBProductionArrangement(**arrangement.dict())
    db.add(db_arrangement)
    db.commit()
    db.refresh(db_arrangement)
    return db_arrangement

@router.put("/{arrangement_id}", response_model=ProductionArrangement)
@router.put("/{arrangement_id}/", response_model=ProductionArrangement)
async def update_production_arrangement(arrangement_id: int, arrangement_update: ProductionArrangementUpdate, db: Session = Depends(get_db)):
    """更新生產安排"""
    arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
    if not arrangement:
        raise HTTPException(status_code=404, detail="生產安排不存在")

    # 更新欄位
    update_data = arrangement_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(arrangement, field, value)

    db.commit()
    db.refresh(arrangement)
    return arrangement

@router.delete("/{arrangement_id}")
@router.delete("/{arrangement_id}/")
async def delete_production_arrangement(arrangement_id: int, db: Session = Depends(get_db)):
    """刪除生產安排"""
    arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
    if not arrangement:
        raise HTTPException(status_code=404, detail="生產安排不存在")

    db.delete(arrangement)
    db.commit()
    
    return {"message": "生產安排已刪除"}

@router.get("/customers/list")
async def get_customers_list(db: Session = Depends(get_db)):
    """取得所有活躍客戶列表"""
    from database import Customer as DBCustomer
    customers = db.query(DBCustomer).filter(DBCustomer.is_active == True).all()
    return [
        {
            "id": customer.id,
            "customer_code": customer.customer_code,
            "company_name": customer.company_name,
            "contact_person": customer.contact_person,
            "display_name": f"{customer.customer_code} - {customer.company_name}"
        }
        for customer in customers
    ]

@router.get("/materials/list")
async def get_materials_list(db: Session = Depends(get_db)):
    """取得所有材質代號列表"""
    materials = db.query(DBProductionArrangement.material_code).distinct().filter(DBProductionArrangement.material_code.isnot(None)).all()
    return [m.material_code for m in materials if m.material_code]

@router.post("/{arrangement_id}/start")
async def start_production(arrangement_id: int, db: Session = Depends(get_db)):
    """開始生產"""
    arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
    if not arrangement:
        raise HTTPException(status_code=404, detail="生產安排不存在")

    arrangement.production_status = "in_progress"
    arrangement.start_date = date.today()
    
    db.commit()
    db.refresh(arrangement)
    
    return arrangement

@router.post("/{arrangement_id}/complete")
async def complete_production(arrangement_id: int, actual_quantity: Optional[int] = None, db: Session = Depends(get_db)):
    """完成生產"""
    arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
    if not arrangement:
        raise HTTPException(status_code=404, detail="生產安排不存在")

    arrangement.production_status = "completed"
    arrangement.completion_date = date.today()
    
    if actual_quantity is not None:
        arrangement.actual_quantity = actual_quantity
    
    db.commit()
    db.refresh(arrangement)
    
    return arrangement

@router.post("/batch-update-sequence")
async def batch_update_sequence(sequence_updates: List[dict], db: Session = Depends(get_db)):
    """批量更新序號"""
    try:
        for update in sequence_updates:
            arrangement_id = update.get("id")
            new_sequence = update.get("sequence_no")
            
            if arrangement_id and new_sequence is not None:
                arrangement = db.query(DBProductionArrangement).filter(DBProductionArrangement.id == arrangement_id).first()
                if arrangement:
                    arrangement.sequence_no = new_sequence
        
        db.commit()
        return {"message": "序號更新成功"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"更新失敗: {str(e)}")

@router.get("/dashboard/summary")
async def get_production_summary(db: Session = Depends(get_db)):
    """取得生產概況統計"""
    total = db.query(DBProductionArrangement).count()
    pending = db.query(DBProductionArrangement).filter(DBProductionArrangement.production_status == "pending").count()
    in_progress = db.query(DBProductionArrangement).filter(DBProductionArrangement.production_status == "in_progress").count()
    completed = db.query(DBProductionArrangement).filter(DBProductionArrangement.production_status == "completed").count()
    
    return {
        "total": total,
        "pending": pending,
        "in_progress": in_progress,
        "completed": completed,
        "completion_rate": round((completed / total * 100) if total > 0 else 0, 1)
    }
