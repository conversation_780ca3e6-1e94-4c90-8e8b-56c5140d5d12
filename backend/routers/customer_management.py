from fastapi import APIRouter, Query, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import func
from database import get_db, Customer as DBCustomer, CustomerOrder as DBCustomerOrder
import math

router = APIRouter()

class CustomerStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    POTENTIAL = "potential"

class Customer(BaseModel):
    id: int
    customer_code: str  # 公司簡稱
    company_name: str  # 公司全稱/Tên đầy đủ
    contact_person: Optional[str] = None
    phone: Optional[str] = None  # 電話/SĐT
    email: Optional[str] = None  # 邮箱/EMAIL
    address: Optional[str] = None  # 地址/Địa chỉ
    tax_id: Optional[str] = None  # 稅號/MST
    invoice_required: bool = False  # 是否開票/HD
    company_shipping_fee: float = 0.0  # 運費 公司車/PHÍ VẬN CHUYỂN Anh Ty
    external_shipping_fee: float = 0.0  # 運費 外車/PHÍ VẬN CHUYỂN xe ngoài
    payment_terms: int = 30  # 帳期/NGÀY CÔNG NỢ (天數)
    contract_info: Optional[str] = None  # 合約/HỢP ĐỒNG
    contract_returned: bool = False  # ĐÃ NHẬN LẠI HỢP ĐỒNG 合約已取回
    notes: Optional[str] = None  # 备注/GHI CHÚ
    credit_limit: Optional[float] = 0.0
    status: CustomerStatus = CustomerStatus.ACTIVE
    created_at: datetime
    updated_at: datetime
    # 銷售統計欄位
    total_orders: Optional[int] = 0
    total_sales_amount: Optional[float] = 0.0
    current_month_sales: Optional[float] = 0.0
    last_month_sales: Optional[float] = 0.0

class CustomerCreate(BaseModel):
    customer_code: str  # 公司簡稱
    company_name: str  # 公司全稱/Tên đầy đủ
    contact_person: Optional[str] = None
    phone: Optional[str] = None  # 電話/SĐT
    email: Optional[str] = None  # 邮箱/EMAIL
    address: Optional[str] = None  # 地址/Địa chỉ
    tax_id: Optional[str] = None  # 稅號/MST
    invoice_required: bool = False  # 是否開票/HD
    company_shipping_fee: float = 0.0  # 運費 公司車/PHÍ VẬN CHUYỂN Anh Ty
    external_shipping_fee: float = 0.0  # 運費 外車/PHÍ VẬN CHUYỂN xe ngoài
    payment_terms: int = 30  # 帳期/NGÀY CÔNG NỢ (天數)
    contract_info: Optional[str] = None  # 合約/HỢP ĐỒNG
    contract_returned: bool = False  # ĐÃ NHẬN LẠI HỢP ĐỒNG 合約已取回
    notes: Optional[str] = None  # 备注/GHI CHÚ
    credit_limit: float = 0.0

class CustomerSalesAnalysis(BaseModel):
    customer_id: int
    customer_name: str
    total_orders: int
    total_sales_amount: float
    current_month_sales: float
    last_month_sales: float
    growth_rate: float
    average_order_value: float
    last_order_date: Optional[date] = None
    top_products: List[Dict[str, Any]]

class CustomerProductAnalysis(BaseModel):
    customer_id: int
    customer_name: str
    products: List[Dict[str, Any]]
    total_products: int
    total_quantity: int
    total_value: float
    cost_analysis: Dict[str, float]

class PaginatedCustomerResponse(BaseModel):
    data: List[Customer]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedCustomerResponse)
async def get_customers(
    page: int = Query(1, ge=1),
    pageSize: int = Query(50, ge=1, le=100),
    status: Optional[CustomerStatus] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """取得客戶列表"""
    query = db.query(DBCustomer)

    # 應用篩選條件
    if status:
        active_status = status == CustomerStatus.ACTIVE
        query = query.filter(DBCustomer.is_active == active_status)
    else:
        # 預設只顯示活躍的客戶
        query = query.filter(DBCustomer.is_active == True)

    if search:
        query = query.filter(
            (DBCustomer.company_name.contains(search)) |
            (DBCustomer.customer_code.contains(search)) |
            (DBCustomer.contact_person.contains(search))
        )

    # 計算總筆數
    total = query.count()

    # 計算分頁參數
    skip = (page - 1) * pageSize
    totalPages = math.ceil(total / pageSize) if total > 0 else 0

    # 查詢分頁資料
    customers = query.offset(skip).limit(pageSize).all()

    # 轉換為響應格式並計算銷售統計
    result = []
    for customer in customers:
        # 計算客戶的銷售統計
        # 查詢客戶的訂單統計
        orders_query = db.query(DBCustomerOrder).filter(DBCustomerOrder.customer_id == customer.id)
        total_orders = orders_query.count()
        total_sales = orders_query.with_entities(func.sum(DBCustomerOrder.total_amount)).scalar() or 0

        # 計算本月銷售額
        from datetime import timedelta
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        current_month_sales = orders_query.filter(
            DBCustomerOrder.order_date >= current_month_start
        ).with_entities(func.sum(DBCustomerOrder.total_amount)).scalar() or 0

        # 計算上月銷售額
        last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        last_month_end = current_month_start - timedelta(days=1)
        last_month_sales = orders_query.filter(
            DBCustomerOrder.order_date >= last_month_start,
            DBCustomerOrder.order_date <= last_month_end
        ).with_entities(func.sum(DBCustomerOrder.total_amount)).scalar() or 0

        result.append({
            "id": customer.id,
            "customer_code": customer.customer_code,
            "company_name": customer.company_name,
            "contact_person": customer.contact_person,
            "phone": customer.phone,
            "email": customer.email,
            "address": customer.address,
            "tax_id": customer.tax_id,
            "invoice_required": getattr(customer, 'invoice_required', False),
            "company_shipping_fee": getattr(customer, 'company_shipping_fee', 0.0),
            "external_shipping_fee": getattr(customer, 'external_shipping_fee', 0.0),
            "payment_terms": getattr(customer, 'payment_terms_int', 30),
            "contract_info": getattr(customer, 'contract_info', None),
            "contract_returned": getattr(customer, 'contract_returned', False),
            "notes": getattr(customer, 'notes', None),
            "credit_limit": customer.credit_limit,
            "status": CustomerStatus.ACTIVE if customer.is_active else CustomerStatus.INACTIVE,
            "created_at": customer.created_at,
            "updated_at": customer.updated_at,
            # 額外的銷售統計資料（前端可以使用）
            "total_orders": total_orders,
            "total_sales_amount": float(total_sales),
            "current_month_sales": float(current_month_sales),
            "last_month_sales": float(last_month_sales)
        })

    return PaginatedCustomerResponse(
        data=result,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=totalPages
    )

@router.post("/", response_model=Customer)
async def create_customer(customer: CustomerCreate, db: Session = Depends(get_db)):
    """建立客戶資料"""
    # 檢查客戶代碼是否已存在
    existing_customer = db.query(DBCustomer).filter(DBCustomer.customer_code == customer.customer_code).first()
    if existing_customer:
        raise HTTPException(status_code=400, detail="客戶代碼已存在")

    # 創建新客戶
    db_customer = DBCustomer(
        customer_code=customer.customer_code,
        company_name=customer.company_name,
        contact_person=customer.contact_person,
        phone=customer.phone,
        email=customer.email,
        address=customer.address,
        tax_id=customer.tax_id,
        invoice_required=customer.invoice_required,
        company_shipping_fee=customer.company_shipping_fee,
        external_shipping_fee=customer.external_shipping_fee,
        payment_terms=customer.payment_terms,
        contract_info=customer.contract_info,
        contract_returned=customer.contract_returned,
        notes=customer.notes,
        credit_limit=customer.credit_limit,
        is_active=True
    )

    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)

    return {
        "id": db_customer.id,
        "customer_code": db_customer.customer_code,
        "company_name": db_customer.company_name,
        "contact_person": db_customer.contact_person,
        "phone": db_customer.phone,
        "email": db_customer.email,
        "address": db_customer.address,
        "tax_id": db_customer.tax_id,
        "payment_terms": customer.payment_terms,
        "credit_limit": db_customer.credit_limit,
        "status": CustomerStatus.ACTIVE,
        "created_at": db_customer.created_at,
        "updated_at": db_customer.updated_at
    }

@router.get("/{customer_id}", response_model=Customer)
@router.get("/{customer_id}/", response_model=Customer)
async def get_customer(customer_id: int, db: Session = Depends(get_db)):
    """取得客戶詳情"""
    customer = db.query(DBCustomer).filter(DBCustomer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客戶不存在")

    return {
        "id": customer.id,
        "customer_code": customer.customer_code,
        "company_name": customer.company_name,
        "contact_person": customer.contact_person,
        "phone": customer.phone,
        "email": customer.email,
        "address": customer.address,
        "tax_id": customer.tax_id,
        "payment_terms": 30,  # 從字符串解析或設置默認值
        "credit_limit": customer.credit_limit,
        "status": CustomerStatus.ACTIVE if customer.is_active else CustomerStatus.INACTIVE,
        "created_at": customer.created_at,
        "updated_at": customer.updated_at
    }

@router.put("/{customer_id}", response_model=Customer)
@router.put("/{customer_id}/", response_model=Customer)
async def update_customer(customer_id: int, customer_update: CustomerCreate, db: Session = Depends(get_db)):
    """更新客戶資料"""
    customer = db.query(DBCustomer).filter(DBCustomer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客戶不存在")

    # 檢查客戶代碼是否與其他客戶衝突
    if customer_update.customer_code != customer.customer_code:
        existing_customer = db.query(DBCustomer).filter(
            DBCustomer.customer_code == customer_update.customer_code,
            DBCustomer.id != customer_id
        ).first()
        if existing_customer:
            raise HTTPException(status_code=400, detail="客戶代碼已存在")

    # 更新客戶資料
    customer.customer_code = customer_update.customer_code
    customer.company_name = customer_update.company_name
    customer.contact_person = customer_update.contact_person
    customer.phone = customer_update.phone
    customer.email = customer_update.email
    customer.address = customer_update.address
    customer.tax_id = customer_update.tax_id
    customer.invoice_required = customer_update.invoice_required
    customer.company_shipping_fee = customer_update.company_shipping_fee
    customer.external_shipping_fee = customer_update.external_shipping_fee
    customer.payment_terms_int = customer_update.payment_terms
    customer.contract_info = customer_update.contract_info
    customer.contract_returned = customer_update.contract_returned
    customer.notes = customer_update.notes
    customer.credit_limit = customer_update.credit_limit
    customer.updated_at = datetime.now()

    db.commit()
    db.refresh(customer)

    return {
        "id": customer.id,
        "customer_code": customer.customer_code,
        "company_name": customer.company_name,
        "contact_person": customer.contact_person,
        "phone": customer.phone,
        "email": customer.email,
        "address": customer.address,
        "tax_id": customer.tax_id,
        "payment_terms": customer_update.payment_terms,
        "credit_limit": customer.credit_limit,
        "status": CustomerStatus.ACTIVE if customer.is_active else CustomerStatus.INACTIVE,
        "created_at": customer.created_at,
        "updated_at": customer.updated_at
    }

@router.delete("/{customer_id}")
@router.delete("/{customer_id}/")
async def delete_customer(customer_id: int, db: Session = Depends(get_db)):
    """刪除客戶資料（軟刪除）"""
    customer = db.query(DBCustomer).filter(DBCustomer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客戶不存在")

    # 軟刪除 - 設為非活躍狀態
    customer.is_active = False
    db.commit()

    return {"message": "客戶已刪除"}

@router.get("/{customer_id}/sales-analysis", response_model=CustomerSalesAnalysis)
async def get_customer_sales_analysis(customer_id: int):
    """取得客戶銷售分析"""
    # TODO: 實際計算銷售分析
    return CustomerSalesAnalysis(
        customer_id=customer_id,
        customer_name="ABC公司",
        total_orders=25,
        total_sales_amount=500000.0,
        current_month_sales=50000.0,
        last_month_sales=45000.0,
        growth_rate=11.11,
        average_order_value=20000.0,
        last_order_date=date.today(),
        top_products=[
            {"product_name": "包裝盒A", "quantity": 5000, "amount": 75000.0},
            {"product_name": "標籤B", "quantity": 10000, "amount": 30000.0}
        ]
    )

@router.get("/{customer_id}/product-analysis", response_model=CustomerProductAnalysis)
async def get_customer_product_analysis(customer_id: int):
    """取得客戶產品分析"""
    # TODO: 實際計算產品分析
    return CustomerProductAnalysis(
        customer_id=customer_id,
        customer_name="ABC公司",
        products=[
            {
                "product_id": 1,
                "product_name": "包裝盒A",
                "total_quantity": 5000,
                "total_value": 75000.0,
                "unit_price": 15.0,
                "cost": 13.0,
                "profit": 10000.0
            }
        ],
        total_products=3,
        total_quantity=15000,
        total_value=150000.0,
        cost_analysis={
            "total_cost": 130000.0,
            "total_profit": 20000.0,
            "profit_margin": 13.33
        }
    )

@router.get("/{customer_id}/orders")
async def get_customer_orders(
    customer_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None
):
    """取得客戶訂單記錄"""
    # TODO: 實際從資料庫查詢
    return []

@router.get("/reports/sales-ranking")
async def get_customer_sales_ranking(
    start_date: date = Query(...),
    end_date: date = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """取得客戶銷售排名"""
    # TODO: 實際計算銷售排名
    return []

@router.get("/reports/credit-status")
async def get_customer_credit_status():
    """取得客戶信用狀況報表"""
    # TODO: 實際計算信用狀況
    return {
        "total_customers": 150,
        "customers_over_credit_limit": 5,
        "total_outstanding": 250000.0,
        "overdue_amount": 50000.0
    }
