from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from database import get_db, User

router = APIRouter()
security = HTTPBearer()

# 資料模型
class UserLogin(BaseModel):
    username: str
    password: str

class UserCreate(BaseModel):
    username: str
    password: str
    email: str
    full_name: str
    role: str = "user"

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: str
    role: str
    is_active: bool

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

# JWT 設定
SECRET_KEY = "your-secret-key-here"  # 實際部署時應使用環境變數
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 權限等級
ROLES = {
    "admin": ["all"],
    "manager": ["customer_orders", "material_orders", "inventory", "finance", "product_data", "customer_management"],
    "production": ["production_schedule", "inventory"],
    "hr": ["hr_payroll"],
    "user": ["customer_orders", "inventory"]
}

@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """使用者登入"""
    from passlib.context import CryptContext

    # 創建密碼驗證上下文
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    # 查詢用戶
    user = db.query(User).filter(User.username == user_data.username).first()

    if not user or not pwd_context.verify(user_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="使用者名稱或密碼錯誤"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="帳號已被停用"
        )

    # 建立簡單的 token (實際應使用 JWT)
    access_token = f"token-{user.username}-{datetime.now().timestamp()}"

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate):
    """使用者註冊"""
    # TODO: 實際建立使用者到資料庫
    return {
        "id": 1,
        "username": user_data.username,
        "email": user_data.email,
        "full_name": user_data.full_name,
        "role": user_data.role,
        "is_active": True
    }

@router.get("/me", response_model=UserResponse)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """取得當前使用者資訊"""
    try:
        token = credentials.credentials
        # 簡單的 token 驗證 (實際應使用 JWT)
        if token.startswith("token-"):
            username = token.split("-")[1]
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="無效的認證憑證"
            )

        # 從資料庫取得使用者資訊
        user = db.query(User).filter(User.username == username).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用戶不存在"
            )

        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role.value,
            "is_active": user.is_active
        }
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無效的認證憑證"
        )

@router.get("/permissions")
async def get_user_permissions(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """取得使用者權限"""
    try:
        token = credentials.credentials
        # 簡單的 token 驗證 (實際應使用 JWT)
        if token.startswith("mock-token-"):
            role = "admin"
        else:
            role = "user"

        permissions = ROLES.get(role, [])

        return {
            "role": role,
            "permissions": permissions
        }
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無效的認證憑證"
        )

@router.post("/logout")
async def logout():
    """使用者登出"""
    # JWT 是無狀態的，實際登出通常在前端處理
    return {"message": "登出成功"}
