from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel, field_validator
from typing import List, Optional, Union
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, CardboardPricing as DBCardboardPricing
import math

router = APIRouter()

# Pydantic 模型
class CardboardPricingBase(BaseModel):
    date: Optional[str] = None
    supplier_code: Optional[str] = None
    material_weight: Optional[str] = None
    material_code: Optional[str] = None
    unit_price: Optional[float] = None
    corrugated_type: Optional[str] = None
    currency: str = "VND"
    status: str = "active"
    effective_date: Optional[str] = None
    expiry_date: Optional[str] = None
    notes: Optional[str] = None
    created_by: Optional[str] = None

class CardboardPricingCreate(CardboardPricingBase):
    pass

class CardboardPricingUpdate(BaseModel):
    date: Optional[str] = None
    supplier_code: Optional[str] = None
    material_weight: Optional[str] = None
    material_code: Optional[str] = None
    unit_price: Optional[float] = None
    corrugated_type: Optional[str] = None
    currency: Optional[str] = None
    status: Optional[str] = None
    effective_date: Optional[str] = None
    expiry_date: Optional[str] = None
    notes: Optional[str] = None

class CardboardPricing(CardboardPricingBase):
    id: int
    created_at: datetime
    updated_at: datetime

    @field_validator('date', 'effective_date', 'expiry_date', mode='before')
    @classmethod
    def serialize_date(cls, v):
        if v is None:
            return None
        if isinstance(v, date):
            return v.strftime('%Y-%m-%d')
        return v

    class Config:
        from_attributes = True

class PaginatedCardboardPricingResponse(BaseModel):
    data: List[CardboardPricing]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedCardboardPricingResponse)
@router.get("", response_model=PaginatedCardboardPricingResponse)
async def get_cardboard_pricing(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    supplier_code: Optional[str] = None,
    material_code: Optional[str] = None,
    corrugated_type: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """取得紙板單價列表"""
    query = db.query(DBCardboardPricing)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBCardboardPricing.supplier_code.contains(search)) |
            (DBCardboardPricing.material_code.contains(search)) |
            (DBCardboardPricing.corrugated_type.contains(search))
        )
    
    if supplier_code:
        query = query.filter(DBCardboardPricing.supplier_code.contains(supplier_code))
    
    if material_code:
        query = query.filter(DBCardboardPricing.material_code.contains(material_code))
        
    if corrugated_type:
        query = query.filter(DBCardboardPricing.corrugated_type.contains(corrugated_type))
        
    if status:
        query = query.filter(DBCardboardPricing.status == status)
        
    if date_from:
        query = query.filter(DBCardboardPricing.date >= date_from)
        
    if date_to:
        query = query.filter(DBCardboardPricing.date <= date_to)

    # 計算總數
    total = query.count()
    
    # 分頁 - 按日期倒序排列
    offset = (page - 1) * pageSize
    pricing_records = query.order_by(DBCardboardPricing.date.desc()).offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedCardboardPricingResponse(
        data=pricing_records,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{pricing_id}", response_model=CardboardPricing)
@router.get("/{pricing_id}/", response_model=CardboardPricing)
async def get_cardboard_pricing_item(pricing_id: int, db: Session = Depends(get_db)):
    """取得單一紙板單價資料"""
    pricing = db.query(DBCardboardPricing).filter(DBCardboardPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="紙板單價不存在")
    return pricing

@router.post("/", response_model=CardboardPricing, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=CardboardPricing, status_code=status.HTTP_201_CREATED)
async def create_cardboard_pricing(pricing: CardboardPricingCreate, db: Session = Depends(get_db)):
    """建立紙板單價"""
    # 轉換日期字串為 date 物件
    pricing_data = pricing.model_dump()

    # 處理日期欄位
    for field in ['date', 'effective_date', 'expiry_date']:
        if pricing_data.get(field):
            try:
                pricing_data[field] = datetime.strptime(pricing_data[field], '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(status_code=422, detail=f'{field} 日期格式錯誤，請使用 YYYY-MM-DD 格式')

    # 創建新紙板單價
    db_pricing = DBCardboardPricing(**pricing_data)
    db.add(db_pricing)
    db.commit()
    db.refresh(db_pricing)
    return db_pricing

@router.put("/{pricing_id}", response_model=CardboardPricing)
@router.put("/{pricing_id}/", response_model=CardboardPricing)
async def update_cardboard_pricing(pricing_id: int, pricing_update: CardboardPricingUpdate, db: Session = Depends(get_db)):
    """更新紙板單價"""
    pricing = db.query(DBCardboardPricing).filter(DBCardboardPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="紙板單價不存在")

    # 更新欄位
    update_data = pricing_update.model_dump(exclude_unset=True)

    # 處理日期欄位
    for field in ['date', 'effective_date', 'expiry_date']:
        if field in update_data and update_data[field]:
            try:
                update_data[field] = datetime.strptime(update_data[field], '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(status_code=422, detail=f'{field} 日期格式錯誤，請使用 YYYY-MM-DD 格式')

    for field, value in update_data.items():
        setattr(pricing, field, value)

    db.commit()
    db.refresh(pricing)
    return pricing

@router.delete("/{pricing_id}")
@router.delete("/{pricing_id}/")
async def delete_cardboard_pricing(pricing_id: int, db: Session = Depends(get_db)):
    """刪除紙板單價"""
    pricing = db.query(DBCardboardPricing).filter(DBCardboardPricing.id == pricing_id).first()
    if not pricing:
        raise HTTPException(status_code=404, detail="紙板單價不存在")

    db.delete(pricing)
    db.commit()
    
    return {"message": "紙板單價已刪除"}

@router.get("/suppliers/list")
async def get_suppliers_list(db: Session = Depends(get_db)):
    """取得所有活躍供應商列表"""
    from database import Supplier as DBSupplier
    suppliers = db.query(DBSupplier).filter(DBSupplier.is_active == True).all()
    return [
        {
            "id": supplier.id,
            "supplier_code": supplier.supplier_code,
            "company_name": supplier.company_name,
            "contact_person": supplier.contact_person,
            "display_name": f"{supplier.supplier_code} - {supplier.company_name}"
        }
        for supplier in suppliers
    ]

@router.get("/materials/list")
async def get_materials_list(db: Session = Depends(get_db)):
    """取得所有材質代號列表"""
    # 從紙板單價記錄中獲取不重複的材質代號和克重
    materials_query = db.query(
        DBCardboardPricing.material_code,
        DBCardboardPricing.material_weight
    ).distinct().filter(
        DBCardboardPricing.material_code.isnot(None)
    ).all()

    materials = []
    for idx, (material_code, material_weight) in enumerate(materials_query):
        if material_code:
            materials.append({
                "id": idx + 1,
                "material_code": material_code,
                "material_name": material_code,  # 暫時使用代號作為名稱
                "material_weight": material_weight or 0,
                "display_name": f"{material_code}{f' ({material_weight}g)' if material_weight else ''}"
            })

    return materials

@router.get("/corrugated-types/list")
async def get_corrugated_types_list(db: Session = Depends(get_db)):
    """取得所有瓦楞類型列表"""
    # 返回固定的5種瓦楞類型
    corrugated_types = [
        {
            "id": 1,
            "corrugated_code": "C",
            "corrugated_name": "C楞",
            "display_name": "C"
        },
        {
            "id": 2,
            "corrugated_code": "B",
            "corrugated_name": "B楞",
            "display_name": "B"
        },
        {
            "id": 3,
            "corrugated_code": "E",
            "corrugated_name": "E楞",
            "display_name": "E"
        },
        {
            "id": 4,
            "corrugated_code": "CB",
            "corrugated_name": "CB楞",
            "display_name": "CB"
        },
        {
            "id": 5,
            "corrugated_code": "EB",
            "corrugated_name": "EB楞",
            "display_name": "EB"
        }
    ]

    return corrugated_types

@router.get("/dashboard/summary")
async def get_pricing_summary(db: Session = Depends(get_db)):
    """取得紙板單價概況統計"""
    total = db.query(DBCardboardPricing).count()
    active = db.query(DBCardboardPricing).filter(DBCardboardPricing.status == "active").count()
    inactive = db.query(DBCardboardPricing).filter(DBCardboardPricing.status == "inactive").count()
    
    # 供應商數量
    supplier_count = db.query(DBCardboardPricing.supplier_code).distinct().count()
    
    # 材質數量
    material_count = db.query(DBCardboardPricing.material_code).distinct().count()
    
    # 平均價格
    avg_price = db.query(DBCardboardPricing.unit_price).filter(DBCardboardPricing.status == "active").all()
    average_price = sum([price[0] for price in avg_price if price[0]]) / len(avg_price) if avg_price else 0
    
    return {
        "total_records": total,
        "active_records": active,
        "inactive_records": inactive,
        "supplier_count": supplier_count,
        "material_count": material_count,
        "average_price": round(average_price, 2)
    }

@router.post("/batch-update-status")
async def batch_update_status(pricing_ids: List[int], new_status: str, db: Session = Depends(get_db)):
    """批量更新狀態"""
    try:
        updated_count = db.query(DBCardboardPricing).filter(
            DBCardboardPricing.id.in_(pricing_ids)
        ).update({"status": new_status}, synchronize_session=False)
        
        db.commit()
        return {"message": f"已更新 {updated_count} 筆記錄的狀態為 {new_status}"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"批量更新失敗: {str(e)}")

@router.get("/search/by-material")
async def search_by_material(
    material_code: str,
    supplier_code: Optional[str] = None,
    corrugated_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """根據材質代號搜尋價格"""
    query = db.query(DBCardboardPricing).filter(
        DBCardboardPricing.material_code == material_code,
        DBCardboardPricing.status == "active"
    )

    if supplier_code:
        query = query.filter(DBCardboardPricing.supplier_code == supplier_code)

    if corrugated_type:
        query = query.filter(DBCardboardPricing.corrugated_type == corrugated_type)

    results = query.order_by(DBCardboardPricing.date.desc()).all()
    return results

@router.get("/search/by-weight")
async def search_by_weight(
    material_weight: int,
    supplier_code: Optional[str] = None,
    corrugated_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """根據材質克重搜尋價格和材質代號"""
    # 將數字轉換為字串進行查詢，支援精確匹配和複合格式匹配
    weight_str = str(material_weight)

    query = db.query(DBCardboardPricing).filter(
        # 支援多種匹配方式：
        # 1. 精確匹配（單一克重）
        (DBCardboardPricing.material_weight == weight_str) |
        # 2. 包含匹配（複合格式如 120/90/90/90/120）
        (DBCardboardPricing.material_weight.contains(weight_str)) |
        # 3. 開頭匹配（如 120/xxx）
        (DBCardboardPricing.material_weight.like(f"{weight_str}/%")) |
        # 4. 中間匹配（如 xxx/120/xxx）
        (DBCardboardPricing.material_weight.like(f"%/{weight_str}/%")) |
        # 5. 結尾匹配（如 xxx/120）
        (DBCardboardPricing.material_weight.like(f"%/{weight_str}")),
        DBCardboardPricing.status == "active"
    )

    if supplier_code:
        query = query.filter(DBCardboardPricing.supplier_code == supplier_code)

    if corrugated_type:
        query = query.filter(DBCardboardPricing.corrugated_type == corrugated_type)

    results = query.order_by(DBCardboardPricing.date.desc()).all()
    return results
