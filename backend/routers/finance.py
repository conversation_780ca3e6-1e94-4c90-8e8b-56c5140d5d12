from fastapi import APIRouter, Query, Depends
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from database import (
    get_db, CustomerOrder, MaterialOrder, InventoryItem,
    StockTransaction, Employee, OrderStatus
)
from datetime import timedelta

router = APIRouter()

class ReportPeriod(str, Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"

class FinancialReport(BaseModel):
    period: str
    revenue: float
    cost: float
    gross_profit: float
    expenses: float
    net_profit: float
    profit_margin: float

class AccountReceivable(BaseModel):
    customer_id: int
    customer_name: str
    invoice_no: str
    invoice_date: date
    due_date: date
    amount: float
    paid_amount: float
    outstanding_amount: float
    overdue_days: int

class AccountPayable(BaseModel):
    supplier_id: int
    supplier_name: str
    invoice_no: str
    invoice_date: date
    due_date: date
    amount: float
    paid_amount: float
    outstanding_amount: float
    overdue_days: int

@router.get("/reports/{period}", response_model=FinancialReport)
async def get_financial_report(
    period: ReportPeriod,
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """取得財務報表"""

    # 1. 計算收入 - 來自已完成的客戶訂單
    revenue_result = db.query(func.sum(CustomerOrder.total_amount)).filter(
        and_(
            CustomerOrder.status == OrderStatus.COMPLETED,
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).scalar()
    revenue = float(revenue_result or 0)

    # 2. 計算直接成本 - 來自原物料採購
    material_cost_result = db.query(func.sum(MaterialOrder.total_amount)).filter(
        and_(
            MaterialOrder.order_date >= start_date,
            MaterialOrder.order_date <= end_date
        )
    ).scalar()
    material_cost = float(material_cost_result or 0)

    # 3. 計算庫存消耗成本 - 出庫交易
    inventory_cost_result = db.query(func.sum(StockTransaction.unit_cost * StockTransaction.quantity)).filter(
        and_(
            StockTransaction.transaction_type == "out",
            StockTransaction.created_at >= start_date,
            StockTransaction.created_at <= end_date
        )
    ).scalar()
    inventory_cost = float(inventory_cost_result or 0)

    # 總成本
    total_cost = material_cost + inventory_cost

    # 4. 計算毛利
    gross_profit = revenue - total_cost

    # 5. 計算營運費用 - 員工薪資
    # 假設每月薪資，根據期間計算
    days_in_period = (end_date - start_date).days + 1
    months_in_period = days_in_period / 30.0  # 簡化計算

    salary_result = db.query(func.sum(Employee.salary)).filter(
        Employee.is_active == True
    ).scalar()
    salary_expenses = float(salary_result or 0) * months_in_period

    # 其他固定費用 (可以後續從設定表讀取)
    other_expenses = 50000.0 * months_in_period  # 假設每月固定費用5萬

    total_expenses = salary_expenses + other_expenses

    # 6. 計算淨利
    net_profit = gross_profit - total_expenses

    # 7. 計算利潤率
    profit_margin = (net_profit / revenue * 100) if revenue > 0 else 0

    return FinancialReport(
        period=f"{start_date} 至 {end_date}",
        revenue=revenue,
        cost=total_cost,
        gross_profit=gross_profit,
        expenses=total_expenses,
        net_profit=net_profit,
        profit_margin=round(profit_margin, 2)
    )

@router.get("/receivables", response_model=List[AccountReceivable])
async def get_accounts_receivable(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    overdue_only: bool = False,
    db: Session = Depends(get_db)
):
    """取得應收帳款"""
    from database import Customer

    # 查詢已確認但未完成的訂單作為應收帳款
    query = db.query(CustomerOrder, Customer).join(
        Customer, CustomerOrder.customer_id == Customer.id
    ).filter(
        CustomerOrder.status.in_([OrderStatus.CONFIRMED, OrderStatus.PROCESSING])
    )

    if overdue_only:
        # 只顯示逾期的訂單 (交貨日期已過)
        query = query.filter(CustomerOrder.delivery_date < date.today())

    orders = query.offset(skip).limit(limit).all()

    receivables = []
    for order, customer in orders:
        # 假設付款條件從客戶資料解析 (例如: "30天" -> 30)
        try:
            payment_days = int(customer.payment_terms.replace("天", "").replace("月結", "")) if customer.payment_terms else 30
        except:
            payment_days = 30

        due_date = order.order_date + timedelta(days=payment_days)

        receivables.append(AccountReceivable(
            customer_id=customer.id,
            customer_name=customer.company_name,
            invoice_no=order.order_no,
            invoice_date=order.order_date,
            due_date=due_date,
            amount=order.total_amount,
            paid_amount=0.0,  # TODO: 實現付款記錄
            outstanding_amount=order.total_amount,
            overdue_days=max(0, (date.today() - due_date).days)
        ))

    return receivables

@router.get("/payables", response_model=List[AccountPayable])
async def get_accounts_payable(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    overdue_only: bool = False,
    db: Session = Depends(get_db)
):
    """取得應付帳款"""

    # 查詢已訂購但未完成的原物料訂單作為應付帳款
    query = db.query(MaterialOrder).filter(
        MaterialOrder.status.in_(["pending", "ordered"])
    )

    if overdue_only:
        # 只顯示逾期的訂單
        query = query.filter(MaterialOrder.expected_date < date.today())

    orders = query.offset(skip).limit(limit).all()

    payables = []
    for order in orders:
        # 假設付款條件為30天
        payment_days = 30
        due_date = order.order_date + timedelta(days=payment_days)

        payables.append(AccountPayable(
            supplier_id=1,  # TODO: 實現供應商ID
            supplier_name=order.supplier,
            invoice_no=order.order_no,
            invoice_date=order.order_date,
            due_date=due_date,
            amount=order.total_amount,
            paid_amount=0.0,  # TODO: 實現付款記錄
            outstanding_amount=order.total_amount,
            overdue_days=max(0, (date.today() - due_date).days)
        ))

    return payables

@router.get("/cash-flow")
async def get_cash_flow_report(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """取得現金流量報表"""

    # 現金流入 - 來自已完成的客戶訂單
    cash_inflow_result = db.query(func.sum(CustomerOrder.total_amount)).filter(
        and_(
            CustomerOrder.status == OrderStatus.COMPLETED,
            CustomerOrder.order_date >= start_date,
            CustomerOrder.order_date <= end_date
        )
    ).scalar()
    cash_inflow = float(cash_inflow_result or 0)

    # 現金流出 - 來自原物料採購
    material_outflow_result = db.query(func.sum(MaterialOrder.total_amount)).filter(
        and_(
            MaterialOrder.order_date >= start_date,
            MaterialOrder.order_date <= end_date
        )
    ).scalar()
    material_outflow = float(material_outflow_result or 0)

    # 薪資支出
    days_in_period = (end_date - start_date).days + 1
    months_in_period = days_in_period / 30.0

    salary_outflow_result = db.query(func.sum(Employee.salary)).filter(
        Employee.is_active == True
    ).scalar()
    salary_outflow = float(salary_outflow_result or 0) * months_in_period

    # 其他營運支出
    other_outflow = 50000.0 * months_in_period

    total_cash_outflow = material_outflow + salary_outflow + other_outflow
    net_cash_flow = cash_inflow - total_cash_outflow

    return {
        "period": f"{start_date} 至 {end_date}",
        "cash_inflow": cash_inflow,
        "cash_outflow": total_cash_outflow,
        "net_cash_flow": net_cash_flow,
        "details": {
            "revenue": cash_inflow,
            "material_costs": material_outflow,
            "salary_costs": salary_outflow,
            "other_costs": other_outflow
        }
    }
