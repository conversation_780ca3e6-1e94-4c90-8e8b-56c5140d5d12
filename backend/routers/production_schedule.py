from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime, date
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from database import get_db, ProductionTask as DBProductionTask, Workstation as DBWorkstation, CustomerOrder

router = APIRouter()

# 工作站枚舉
class WorkStation(str, Enum):
    PRINTING_1 = "printing_1"          # 印刷機1 (M<PERSON><PERSON> in)
    PRINTING_2 = "printing_2"          # 印刷機2 (<PERSON><PERSON>y Đỏ)
    LAMINATING = "laminating"          # 貼合機 (Máy Dán)
    PRESSING = "pressing"              # 壓合機 (<PERSON><PERSON><PERSON>)
    HAND_LAMINATING = "hand_laminating" # 手貼機 (máy Dán Nhỏ)
    STAPLING_1 = "stapling_1"          # 打釘機1 (M<PERSON>y Đóng Định 1)
    STAPLING_2 = "stapling_2"          # 打釘機2 (<PERSON><PERSON><PERSON>g Định 2)
    STAPLING_3 = "stapling_3"          # 打釘機3 (<PERSON><PERSON><PERSON> 3)
    CREASING_1 = "creasing_1"          # 壓痕機1 (<PERSON><PERSON><PERSON> 1)
    CREASING_2 = "creasing_2"          # 壓痕機2 (Máy Bế 2)
    FILM_COATING = "film_coating"      # 覆膜機 (Máy qua mạng)
    PAPER_CUTTING = "paper_cutting"    # 分紙機 (Máy Xá)
    MANUAL_1 = "manual_1"              # 手工區1 (Dán Tay1)
    MANUAL_2 = "manual_2"              # 手工區2 (Dán Tay2)

# 工作站資訊
WORKSTATION_INFO = {
    WorkStation.PRINTING_1: {"name": "印刷機1", "vietnamese_name": "Máy in"},
    WorkStation.PRINTING_2: {"name": "印刷機2", "vietnamese_name": "Máy Đỏ"},
    WorkStation.LAMINATING: {"name": "貼合機", "vietnamese_name": "Máy Dán"},
    WorkStation.PRESSING: {"name": "壓合機", "vietnamese_name": "Máy Dán Hộp"},
    WorkStation.HAND_LAMINATING: {"name": "手貼機", "vietnamese_name": "máy Dán Nhỏ"},
    WorkStation.STAPLING_1: {"name": "打釘機1", "vietnamese_name": "Máy Đóng Định 1"},
    WorkStation.STAPLING_2: {"name": "打釘機2", "vietnamese_name": "Máy Đóng Định 2"},
    WorkStation.STAPLING_3: {"name": "打釘機3", "vietnamese_name": "Máy Đóng Định 3"},
    WorkStation.CREASING_1: {"name": "壓痕機1", "vietnamese_name": "Máy Bế 1"},
    WorkStation.CREASING_2: {"name": "壓痕機2", "vietnamese_name": "Máy Bế 2"},
    WorkStation.FILM_COATING: {"name": "覆膜機", "vietnamese_name": "Máy qua mạng"},
    WorkStation.PAPER_CUTTING: {"name": "分紙機", "vietnamese_name": "Máy Xá"},
    WorkStation.MANUAL_1: {"name": "手工區1", "vietnamese_name": "Dán Tay1"},
    WorkStation.MANUAL_2: {"name": "手工區2", "vietnamese_name": "Dán Tay2"},
}

# 任務狀態
class TaskStatus(str, Enum):
    PENDING = "pending"      # 待處理
    IN_PROGRESS = "in_progress"  # 進行中
    COMPLETED = "completed"  # 已完成
    PAUSED = "paused"       # 暫停

# 資料模型
class ProductionTask(BaseModel):
    id: int
    order_id: int
    customer_name: str
    product_name: str
    quantity: int
    workstation: WorkStation
    priority: int  # 1-5, 1為最高優先級
    estimated_hours: float
    actual_hours: Optional[float] = None
    status: TaskStatus
    assigned_operator: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    notes: Optional[str] = None

class ProductionScheduleCreate(BaseModel):
    order_id: int
    customer_name: str
    product_name: str
    quantity: int
    workstation: WorkStation
    priority: int = 3
    estimated_hours: float
    assigned_operator: Optional[str] = None
    scheduled_date: date
    notes: Optional[str] = None

class WorkstationStatus(BaseModel):
    workstation: WorkStation
    name: str
    vietnamese_name: str
    current_task: Optional[ProductionTask] = None
    pending_tasks: int
    daily_capacity_hours: float
    utilization_rate: float  # 使用率百分比

class DailySchedule(BaseModel):
    date: date
    workstations: List[WorkstationStatus]
    total_tasks: int
    completed_tasks: int
    in_progress_tasks: int

@router.get("/workstations")
async def get_workstations(db: Session = Depends(get_db)):
    """取得所有工作站資訊"""
    # 先檢查數據庫中是否有工作站數據，如果沒有則初始化
    workstations_count = db.query(DBWorkstation).count()
    if workstations_count == 0:
        # 初始化工作站數據
        for station, info in WORKSTATION_INFO.items():
            workstation = DBWorkstation(
                code=station.value,
                name=info["name"],
                vietnamese_name=info["vietnamese_name"],
                daily_capacity_hours=8.0,
                status="active"
            )
            db.add(workstation)
        db.commit()

    # 從數據庫獲取工作站數據
    workstations = db.query(DBWorkstation).filter(DBWorkstation.is_active == True).all()

    result = []
    for ws in workstations:
        # 計算當前工作站的任務統計
        pending_tasks = db.query(DBProductionTask).filter(
            and_(
                DBProductionTask.workstation == ws.code,
                DBProductionTask.status.in_(["pending", "in_progress"])
            )
        ).count()

        # 計算今日完成的任務數量
        today_completed = db.query(DBProductionTask).filter(
            and_(
                DBProductionTask.workstation == ws.code,
                DBProductionTask.status == "completed",
                func.date(DBProductionTask.end_time) == date.today()
            )
        ).count()

        # 計算使用率（簡化計算）
        total_estimated_hours = db.query(func.sum(DBProductionTask.estimated_hours)).filter(
            and_(
                DBProductionTask.workstation == ws.code,
                DBProductionTask.status == "in_progress"
            )
        ).scalar() or 0

        utilization_rate = min((total_estimated_hours / ws.daily_capacity_hours) * 100, 100)

        result.append({
            "id": ws.id,
            "code": ws.code,
            "name": ws.name,
            "vietnamese_name": ws.vietnamese_name,
            "status": ws.status,
            "dailyCapacity": ws.daily_capacity_hours,
            "pendingTasks": pending_tasks,
            "todayProduced": today_completed,
            "utilizationRate": round(utilization_rate, 1)
        })

    return result

@router.get("/schedule", response_model=DailySchedule)
async def get_daily_schedule(schedule_date: date = Query(default_factory=date.today), db: Session = Depends(get_db)):
    """取得指定日期的生產排程"""
    # 獲取所有工作站
    workstations = db.query(DBWorkstation).filter(DBWorkstation.is_active == True).all()

    workstation_statuses = []
    total_tasks = 0
    completed_tasks = 0
    in_progress_tasks = 0

    for ws in workstations:
        # 獲取該工作站當日的任務
        tasks = db.query(DBProductionTask).filter(
            and_(
                DBProductionTask.workstation == ws.code,
                DBProductionTask.scheduled_date == schedule_date
            )
        ).all()

        # 統計任務狀態
        pending_count = len([t for t in tasks if t.status == "pending"])
        in_progress_count = len([t for t in tasks if t.status == "in_progress"])
        completed_count = len([t for t in tasks if t.status == "completed"])

        total_tasks += len(tasks)
        completed_tasks += completed_count
        in_progress_tasks += in_progress_count

        # 獲取當前進行中的任務
        current_task = None
        current_task_db = next((t for t in tasks if t.status == "in_progress"), None)
        if current_task_db:
            try:
                workstation_enum = WorkStation(current_task_db.workstation)
                status_enum = TaskStatus(current_task_db.status)
                current_task = ProductionTask(
                    id=current_task_db.id,
                    order_id=current_task_db.order_id,
                    customer_name=current_task_db.customer_name,
                    product_name=current_task_db.product_name,
                    quantity=current_task_db.quantity,
                    workstation=workstation_enum,
                    priority=current_task_db.priority,
                    estimated_hours=current_task_db.estimated_hours,
                    actual_hours=current_task_db.actual_hours,
                    status=status_enum,
                    assigned_operator=current_task_db.assigned_operator,
                    start_time=current_task_db.start_time,
                    end_time=current_task_db.end_time,
                    notes=current_task_db.notes
                )
            except ValueError:
                pass

        # 計算使用率
        total_estimated_hours = sum(t.estimated_hours for t in tasks if t.status in ["pending", "in_progress"])
        utilization_rate = min((total_estimated_hours / ws.daily_capacity_hours) * 100, 100)

        try:
            workstation_enum = WorkStation(ws.code)
            workstation_status = WorkstationStatus(
                workstation=workstation_enum,
                name=ws.name,
                vietnamese_name=ws.vietnamese_name,
                current_task=current_task,
                pending_tasks=pending_count,
                daily_capacity_hours=ws.daily_capacity_hours,
                utilization_rate=round(utilization_rate, 1)
            )
            workstation_statuses.append(workstation_status)
        except ValueError:
            continue

    return DailySchedule(
        date=schedule_date,
        workstations=workstation_statuses,
        total_tasks=total_tasks,
        completed_tasks=completed_tasks,
        in_progress_tasks=in_progress_tasks
    )

@router.post("/tasks", response_model=ProductionTask)
async def create_production_task(task: ProductionScheduleCreate, db: Session = Depends(get_db)):
    """建立新的生產任務"""
    # 創建新的生產任務
    db_task = DBProductionTask(
        order_id=task.order_id,
        customer_name=task.customer_name,
        product_name=task.product_name,
        quantity=task.quantity,
        workstation=task.workstation.value,
        priority=task.priority,
        estimated_hours=task.estimated_hours,
        status="pending",
        assigned_operator=task.assigned_operator,
        scheduled_date=task.scheduled_date,
        notes=task.notes
    )

    db.add(db_task)
    db.commit()
    db.refresh(db_task)

    # 返回API模型
    return ProductionTask(
        id=db_task.id,
        order_id=db_task.order_id,
        customer_name=db_task.customer_name,
        product_name=db_task.product_name,
        quantity=db_task.quantity,
        workstation=task.workstation,
        priority=db_task.priority,
        estimated_hours=db_task.estimated_hours,
        actual_hours=db_task.actual_hours,
        status=TaskStatus.PENDING,
        assigned_operator=db_task.assigned_operator,
        start_time=db_task.start_time,
        end_time=db_task.end_time,
        notes=db_task.notes
    )

@router.get("/tasks", response_model=List[ProductionTask])
async def get_production_tasks(
    workstation: Optional[WorkStation] = None,
    status: Optional[TaskStatus] = None,
    date: Optional[date] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """取得生產任務列表"""
    query = db.query(DBProductionTask)

    # 篩選條件
    if workstation:
        query = query.filter(DBProductionTask.workstation == workstation.value)
    if status:
        query = query.filter(DBProductionTask.status == status.value)
    if date:
        query = query.filter(DBProductionTask.scheduled_date == date)

    # 分頁
    tasks = query.offset(skip).limit(limit).all()

    # 轉換為API模型
    result = []
    for task in tasks:
        # 將字符串轉換為枚舉
        try:
            workstation_enum = WorkStation(task.workstation.upper())
        except ValueError:
            workstation_enum = WorkStation.PRINTING_1  # 默認值

        try:
            status_enum = TaskStatus(task.status.upper())
        except ValueError:
            status_enum = TaskStatus.PENDING  # 默認值

        result.append(ProductionTask(
            id=task.id,
            order_id=task.order_id,
            customer_name=task.customer_name,
            product_name=task.product_name,
            quantity=task.quantity,
            workstation=workstation_enum,
            priority=task.priority,
            estimated_hours=task.estimated_hours,
            actual_hours=task.actual_hours,
            status=status_enum,
            assigned_operator=task.assigned_operator,
            start_time=task.start_time,
            end_time=task.end_time,
            notes=task.notes
        ))

    return result

@router.put("/tasks/{task_id}/status")
async def update_task_status(task_id: int, status_data: dict, db: Session = Depends(get_db)):
    """更新任務狀態"""
    task = db.query(DBProductionTask).filter(DBProductionTask.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任務不存在")

    new_status = status_data.get("status")
    task.status = new_status

    # 根據狀態更新時間
    if new_status == "in_progress" and not task.start_time:
        task.start_time = datetime.now()
    elif new_status == "completed" and not task.end_time:
        task.end_time = datetime.now()
        # 計算實際工時
        if task.start_time:
            duration = task.end_time - task.start_time
            task.actual_hours = duration.total_seconds() / 3600

    db.commit()
    return {"message": f"任務 {task_id} 狀態已更新為 {new_status}"}

@router.get("/workstations/{workstation}/tasks", response_model=List[ProductionTask])
async def get_workstation_tasks(workstation: WorkStation):
    """取得特定工作站的任務"""
    # TODO: 實際從資料庫查詢
    return []

@router.get("/analytics/utilization")
async def get_workstation_utilization(
    start_date: date = Query(...),
    end_date: date = Query(...)
):
    """取得工作站使用率分析"""
    # TODO: 實際計算使用率
    utilization_data = {}
    for station, info in WORKSTATION_INFO.items():
        utilization_data[station.value] = {
            "name": info["name"],
            "vietnamese_name": info["vietnamese_name"],
            "utilization_rate": 75.0,
            "total_hours": 40.0,
            "productive_hours": 30.0
        }
    return utilization_data
