from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from database import get_db, Supplier as DBSupplier
import math

router = APIRouter()

# Pydantic 模型
class SupplierBase(BaseModel):
    supplier_code: str
    company_name: str
    company_name_vn: Optional[str] = None
    supplier_type: Optional[str] = None
    address: Optional[str] = None
    tax_id: Optional[str] = None
    invoice_required: bool = False
    phone: Optional[str] = None
    bank_info: Optional[str] = None
    payment_terms: int = 30
    contract_info: Optional[str] = None
    notes: Optional[str] = None

class SupplierCreate(SupplierBase):
    pass

class SupplierUpdate(BaseModel):
    supplier_code: Optional[str] = None
    company_name: Optional[str] = None
    company_name_vn: Optional[str] = None
    supplier_type: Optional[str] = None
    address: Optional[str] = None
    tax_id: Optional[str] = None
    invoice_required: Optional[bool] = None
    phone: Optional[str] = None
    bank_info: Optional[str] = None
    payment_terms: Optional[int] = None
    contract_info: Optional[str] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class Supplier(SupplierBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaginatedSupplierResponse(BaseModel):
    data: List[Supplier]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedSupplierResponse)
@router.get("", response_model=PaginatedSupplierResponse)
async def get_suppliers(
    page: int = Query(1, ge=1),
    pageSize: int = Query(50, ge=1, le=100),
    search: Optional[str] = None,
    supplier_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """取得供應商列表"""
    query = db.query(DBSupplier)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBSupplier.company_name.contains(search)) |
            (DBSupplier.supplier_code.contains(search)) |
            (DBSupplier.phone.contains(search))
        )
    
    if supplier_type:
        query = query.filter(DBSupplier.supplier_type == supplier_type)
    
    if is_active is not None:
        query = query.filter(DBSupplier.is_active == is_active)

    # 計算總數
    total = query.count()
    
    # 分頁
    offset = (page - 1) * pageSize
    suppliers = query.offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedSupplierResponse(
        data=suppliers,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{supplier_id}", response_model=Supplier)
async def get_supplier(supplier_id: int, db: Session = Depends(get_db)):
    """取得單一供應商資料"""
    supplier = db.query(DBSupplier).filter(DBSupplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供應商不存在")
    return supplier

@router.post("/", response_model=Supplier, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=Supplier, status_code=status.HTTP_201_CREATED)
async def create_supplier(supplier: SupplierCreate, db: Session = Depends(get_db)):
    """建立供應商資料"""
    # 檢查供應商代碼是否已存在
    existing_supplier = db.query(DBSupplier).filter(DBSupplier.supplier_code == supplier.supplier_code).first()
    if existing_supplier:
        raise HTTPException(status_code=400, detail="供應商代碼已存在")

    # 創建新供應商
    supplier_data = supplier.dict()
    # 將 company_name 同時賦值給 supplier_name（資料庫必填欄位）
    supplier_data['supplier_name'] = supplier_data['company_name']

    db_supplier = DBSupplier(**supplier_data)
    db.add(db_supplier)
    db.commit()
    db.refresh(db_supplier)
    return db_supplier

@router.put("/{supplier_id}", response_model=Supplier)
async def update_supplier(supplier_id: int, supplier_update: SupplierUpdate, db: Session = Depends(get_db)):
    """更新供應商資料"""
    supplier = db.query(DBSupplier).filter(DBSupplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供應商不存在")

    # 如果更新供應商代碼，檢查是否與其他供應商重複
    if supplier_update.supplier_code and supplier_update.supplier_code != supplier.supplier_code:
        existing_supplier = db.query(DBSupplier).filter(
            DBSupplier.supplier_code == supplier_update.supplier_code,
            DBSupplier.id != supplier_id
        ).first()
        if existing_supplier:
            raise HTTPException(status_code=400, detail="供應商代碼已存在")

    # 更新欄位
    update_data = supplier_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(supplier, field, value)

    db.commit()
    db.refresh(supplier)
    return supplier

@router.delete("/{supplier_id}")
async def delete_supplier(supplier_id: int, db: Session = Depends(get_db)):
    """刪除供應商（軟刪除）"""
    supplier = db.query(DBSupplier).filter(DBSupplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供應商不存在")

    # 軟刪除 - 設為非活躍狀態
    supplier.is_active = False
    db.commit()
    
    return {"message": "供應商已刪除"}

@router.get("/types/list")
async def get_supplier_types(db: Session = Depends(get_db)):
    """取得所有供應商類型"""
    # 返回固定的三種供應商類型
    return [
        "紙板供應商",
        "輔料供應商",
        "紙箱廠(同行)"
    ]
