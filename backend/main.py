from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from database import create_tables, init_test_data

# 導入路由模組
from routers import (
    customer_orders,
    material_orders,
    inventory,
    finance,
    hr_payroll,
    product_data,
    production_schedule,
    production_arrangement,
    order_management,
    cardboard_pricing,
    auxiliary_pricing,
    customer_management,
    supplier_management,
    statements,
    cost_analysis,
    auth,
    users,
    excel_data,
    analytics,
    dashboard
)

# 建立 FastAPI 應用程式
app = FastAPI(
    title="順興公司 ERP 系統 API",
    description="企業資源規劃系統後端 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS 設定 - 開發環境允許所有來源
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://ecuador-civilization-ent-guidance.trycloudflare.com",
        "https://physicians-codes-soa-ferry.trycloudflare.com",
        "https://picture-beginning-warrant-solution.trycloudflare.com",
        "https://reconstruction-bought-direction-comment.trycloudflare.com",
        "https://certification-finger-shine-bears.trycloudflare.com",
        "http://localhost:5173",
        "http://localhost:5174",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 根路由
@app.get("/")
async def root():
    return {
        "message": "順興公司 ERP 系統 API",
        "version": "1.0.0",
        "status": "運行中"
    }

# 健康檢查
@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "系統運行正常"}

# 註冊路由
app.include_router(auth.router, prefix="/api/auth", tags=["權限管理"])
app.include_router(users.router, prefix="/api/users", tags=["用戶管理"])
app.include_router(customer_orders.router, prefix="/api/customer-orders", tags=["客戶訂貨"])
app.include_router(material_orders.router, prefix="/api/material-orders", tags=["原物料訂購"])
app.include_router(inventory.router, prefix="/api/inventory", tags=["倉儲進銷存"])
app.include_router(finance.router, prefix="/api/finance", tags=["財務"])
app.include_router(hr_payroll.router, prefix="/api/hr-payroll", tags=["人事薪資"])
app.include_router(product_data.router, prefix="/api/product-data", tags=["商品資料"])
app.include_router(excel_data.router, prefix="/api/excel-data", tags=["Excel 資料"])
app.include_router(production_schedule.router, prefix="/api/production-schedule", tags=["生產排程"])
app.include_router(production_arrangement.router, prefix="/api/production-arrangement", tags=["生產安排"])
app.include_router(order_management.router, prefix="/api/order-management", tags=["訂單管理"])
app.include_router(cardboard_pricing.router, prefix="/api/cardboard-pricing", tags=["紙板單價"])
app.include_router(auxiliary_pricing.router, prefix="/api/auxiliary-pricing", tags=["輔料單價"])
app.include_router(customer_management.router, prefix="/api/customer-management", tags=["客戶管理"])
app.include_router(supplier_management.router, prefix="/api/supplier-management", tags=["供應商管理"])
app.include_router(statements.router, prefix="/api/statements", tags=["對帳單管理"])
app.include_router(cost_analysis.router, prefix="/api/cost-analysis", tags=["成本分析"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["數據分析"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["儀表板"])

# 全域異常處理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"message": "內部伺服器錯誤", "detail": str(exc)}
    )

# 初始化數據庫
@app.on_event("startup")
def initialize_database():
    create_tables()
    init_test_data()
    print("資料庫初始化完成")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
