from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Foreign<PERSON>ey, Enum, <PERSON><PERSON><PERSON>, Text, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime, date
import os
import enum

# 創建資料庫目錄
os.makedirs("./db", exist_ok=True)

# 資料庫連接
DATABASE_URL = "sqlite:///./db/erp.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})

# 創建 Session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 基本模型
Base = declarative_base()

# 獲取數據庫 Session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 枚舉類型定義
class TransactionType(str, enum.Enum):
    IN = "in"      # 入庫
    OUT = "out"    # 出庫
    ADJUST = "adjust"  # 調整

class OrderStatus(str, enum.Enum):
    PENDING = "pending"      # 待處理
    CONFIRMED = "confirmed"  # 已確認
    PROCESSING = "processing" # 處理中
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消

class UserRole(str, enum.Enum):
    ADMIN = "admin"          # 管理員
    MANAGER = "manager"      # 經理
    EMPLOYEE = "employee"    # 員工
    VIEWER = "viewer"        # 查看者

# 用戶模型
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    role = Column(String, default=UserRole.EMPLOYEE)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 客戶模型
class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    customer_code = Column(String, unique=True, index=True)  # 公司簡稱
    company_name = Column(String, index=True)  # 公司全稱/Tên đầy đủ
    contact_person = Column(String)
    phone = Column(String)  # 電話/SĐT
    email = Column(String)  # 邮箱/EMAIL
    address = Column(Text)  # 地址/Địa chỉ
    tax_id = Column(String)  # 稅號/MST
    invoice_required = Column(Boolean, default=False)  # 是否開票/HD
    company_shipping_fee = Column(Float, default=0.0)  # 運費 公司車/PHÍ VẬN CHUYỂN Anh Ty
    external_shipping_fee = Column(Float, default=0.0)  # 運費 外車/PHÍ VẬN CHUYỂN xe ngoài
    payment_terms = Column(Integer, default=30)  # 帳期/NGÀY CÔNG NỢ (天數)
    contract_info = Column(Text)  # 合約/HỢP ĐỒNG
    contract_returned = Column(Boolean, default=False)  # ĐÃ NHẬN LẠI HỢP ĐỒNG 合約已取回
    notes = Column(Text)  # 备注/GHI CHÚ
    credit_limit = Column(Float, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯到訂單
    orders = relationship("CustomerOrder", back_populates="customer")

# 供應商模型
class Supplier(Base):
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    supplier_code = Column(String, unique=True, index=True)  # 供應商代碼
    supplier_name = Column(String, nullable=False, index=True)  # 供應商名稱（必填）
    supplier_name_vn = Column(String)  # 越南文供應商名稱
    contact_person = Column(String)  # 聯絡人
    phone = Column(String)  # 電話/SĐT
    email = Column(String)  # 電子郵件
    address = Column(Text)  # 地址
    payment_terms = Column(Integer, default=30)  # 帳期/NGÀY CÔNG NỢ (天數)
    status = Column(String, default='active')  # 狀態
    notes = Column(Text)  # 备注/GHI CHÚ
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    # 新增的欄位
    company_name = Column(String, index=True)  # 公司全稱
    company_name_vn = Column(String)  # 越南文公司名稱
    supplier_type = Column(String)  # 種類/類型
    tax_id = Column(String)  # 稅號/MST
    invoice_required = Column(Boolean, default=False)  # 是否開票/HD
    bank_info = Column(Text)  # 銀行資訊/SỐ TÀI KHOẢN
    contract_info = Column(Text)  # 合約/HỢP ĐỒNG
    is_active = Column(Boolean, default=True)

# 產品模型 - 擴展以支援包裝業務
class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)

    # 基本資訊
    quote_date = Column(Date)  # 報價時間/Ngày báo giá
    supplier_code = Column(String)  # 供應商代碼/Mã nhà cung cấp
    customer_name = Column(String)  # 客戶/Khách hàng
    product_code = Column(String, unique=True, index=True)  # 編號/料號/Mã hàng
    product_name = Column(String, index=True)  # 品名/Tên hàng
    mold_number = Column(String)  # 刀模號/số khuôn
    design_number = Column(String)  # 圖稿/số bản in

    # 特殊標記 (有"O"，没有" ")
    has_waterproof = Column(Boolean, default=False)  # 防水/chống thấm
    has_film = Column(Boolean, default=False)  # 過模/qua màng

    # 分類和規格
    category = Column(String)  # 分類/Loại
    finished_size = Column(String)  # 成品規格/Quy cách (mm)

    # 材質資訊
    material_weight = Column(Integer)  # 材質克重/chất lượng giấy (gram)
    material_code = Column(String)  # 材質代号/mã giấy
    paper_width = Column(Float)  # 门幅/KHỔ
    cut_width = Column(Float)  # 切宽/CẮT RỘNG (mm)
    cut_length = Column(Float)  # 切长/CẮT DÀI (mm)
    small_sheets = Column(Integer)  # 小张数/SỐ TẤM NHỎ
    large_sheets = Column(Integer)  # 大张数/SỐ TẤM LỚN
    cutting_sheets = Column(Integer)  # 开张数/SỐ DAO
    corrugated = Column(String)  # 瓦楞/SÓNG
    pressing_lines = Column(String)  # 压线/CÁN LẰN
    meters = Column(Float)  # 米数/SỐ MÉT (m)
    area = Column(Float)  # 平方数/DIỆN TÍCH (m²)

    # 成本資料
    cardboard_unit_price = Column(Float)  # 紙板單價/Đơn Giá vnd/㎡
    subtotal_before_tax = Column(Float)  # 小计/(chưa tính thuế)
    moq = Column(Integer)  # MOQ
    labor_cost = Column(Float)  # 人工成本/Chi phí nhân công
    shipping_fee = Column(Float)  # 运输费/Phí vận chuyển
    film_cost = Column(Float)  # 過模/qua màng
    mold_cost = Column(Float)  # 刀模費/tiền khuôn
    additional_cost = Column(Float)  # 额外开支/Chi phí phát sinh
    total_cost = Column(Float)  # 總成本/Tổng chi phí
    unit_price = Column(String, default='pcs')  # 單價/Đơn giá
    profit_amount = Column(Float)  # 利润/Lợi nhuận
    profit_percentage = Column(Float)  # 利润百分比/Lợi nhuận (%)

    # 系統欄位
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯到 BOM 和訂單項目
    bom_items = relationship("BOMItem", back_populates="product")
    order_items = relationship("CustomerOrderItem", back_populates="product")

# BOM (物料清單) 模型
class BOMItem(Base):
    __tablename__ = "bom_items"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    material_id = Column(Integer, ForeignKey("inventory_items.id"))
    quantity_required = Column(Float)
    unit = Column(String)
    created_at = Column(DateTime, default=datetime.now)

    # 關聯
    product = relationship("Product", back_populates="bom_items")
    material = relationship("InventoryItem")

# 客戶訂單模型
class CustomerOrder(Base):
    __tablename__ = "customer_orders"

    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String, unique=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    order_date = Column(Date, default=date.today)
    delivery_date = Column(Date)
    status = Column(String, default=OrderStatus.PENDING)
    total_amount = Column(Float, default=0)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯
    customer = relationship("Customer", back_populates="orders")
    order_items = relationship("CustomerOrderItem", back_populates="order")

# 客戶訂單項目模型 - 擴展以支援包裝業務
class CustomerOrderItem(Base):
    __tablename__ = "customer_order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("customer_orders.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    product_code = Column(String)  # 產品代碼
    product_name = Column(String)  # 產品名稱
    specifications = Column(String)  # 規格
    quantity = Column(Float)
    unit = Column(String, default='pcs')
    unit_price = Column(Float)
    total_price = Column(Float)

    # 生產相關
    paper_order_qty = Column(Integer)  # 原物料採購數量
    actual_paper_qty = Column(Integer)  # 紙板實際數量
    print_plate = Column(String)  # 印刷版
    mold_no = Column(String)  # 刀模號
    production_status = Column(String)  # 生產狀態

    # 交貨相關
    delivered_qty = Column(Integer, default=0)  # 交貨數量
    remaining_qty = Column(Integer)  # 剩餘訂單總數
    delivery_note_no = Column(String)  # 送貨單號

    notes = Column(Text)

    # 關聯
    order = relationship("CustomerOrder", back_populates="order_items")
    product = relationship("Product", back_populates="order_items")

# 原物料採購訂單模型
class MaterialOrder(Base):
    __tablename__ = "material_orders"

    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String, unique=True, index=True)
    supplier = Column(String)
    order_date = Column(Date, default=date.today)
    expected_date = Column(Date)
    status = Column(String, default=OrderStatus.PENDING)
    total_amount = Column(Float, default=0)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯到採購項目
    order_items = relationship("MaterialOrderItem", back_populates="order")

# 原物料採購項目模型
class MaterialOrderItem(Base):
    __tablename__ = "material_order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("material_orders.id"))
    material_id = Column(Integer, ForeignKey("inventory_items.id"))
    quantity = Column(Float)
    unit_price = Column(Float)
    total_price = Column(Float)
    notes = Column(Text)

    # 關聯
    order = relationship("MaterialOrder", back_populates="order_items")
    material = relationship("InventoryItem")

# 庫存項目模型
class InventoryItem(Base):
    __tablename__ = "inventory_items"

    id = Column(Integer, primary_key=True, index=True)
    item_code = Column(String, unique=True, index=True)
    item_name = Column(String, index=True)
    category = Column(String)
    unit = Column(String)
    current_stock = Column(Float)
    min_stock = Column(Float)
    max_stock = Column(Float)
    unit_cost = Column(Float)
    total_value = Column(Float)
    location = Column(String)
    supplier = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯到交易記錄
    transactions = relationship("StockTransaction", back_populates="item")

# 庫存交易模型
class StockTransaction(Base):
    __tablename__ = "stock_transactions"

    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("inventory_items.id"))
    transaction_type = Column(String)  # in, out, adjust
    quantity = Column(Float)
    unit_cost = Column(Float)
    reference_no = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    # 關聯到庫存項目
    item = relationship("InventoryItem", back_populates="transactions")

# 生產任務模型
class ProductionTask(Base):
    __tablename__ = "production_tasks"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("customer_orders.id"))
    customer_name = Column(String)
    product_name = Column(String)
    quantity = Column(Integer)
    workstation = Column(String)  # 工作站代碼
    priority = Column(Integer, default=3)  # 1-5, 1為最高優先級
    estimated_hours = Column(Float)
    actual_hours = Column(Float, default=0)
    status = Column(String, default="pending")  # pending, in_progress, completed, cancelled
    assigned_operator = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    scheduled_date = Column(Date)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯
    order = relationship("CustomerOrder")

# 工作站模型
class Workstation(Base):
    __tablename__ = "workstations"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String, unique=True, index=True)
    name = Column(String)
    vietnamese_name = Column(String)
    daily_capacity_hours = Column(Float, default=8.0)
    status = Column(String, default="active")  # active, idle, maintenance, offline
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 員工模型
class Employee(Base):
    __tablename__ = "employees"

    id = Column(Integer, primary_key=True, index=True)
    employee_no = Column(String, unique=True, index=True)
    full_name = Column(String)
    department = Column(String)
    position = Column(String)
    hire_date = Column(Date)
    salary = Column(Float)
    phone = Column(String)
    email = Column(String)
    address = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯到打卡記錄和薪資記錄
    attendance_records = relationship("AttendanceRecord", back_populates="employee")
    payroll_records = relationship("PayrollRecord", back_populates="employee")

# 打卡記錄模型
class AttendanceRecord(Base):
    __tablename__ = "attendance_records"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    date = Column(Date)
    check_in_time = Column(DateTime)
    check_out_time = Column(DateTime)
    work_hours = Column(Float, default=0)
    overtime_hours = Column(Float, default=0)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

    # 關聯
    employee = relationship("Employee", back_populates="attendance_records")

# 薪資記錄模型
class PayrollRecord(Base):
    __tablename__ = "payroll_records"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    year = Column(Integer)
    month = Column(Integer)
    base_salary = Column(Float)
    overtime_pay = Column(Float, default=0)
    bonus = Column(Float, default=0)
    deductions = Column(Float, default=0)
    net_salary = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

    # 關聯
    employee = relationship("Employee", back_populates="payroll_records")

# 財務記錄模型
class FinanceRecord(Base):
    __tablename__ = "finance_records"

    id = Column(Integer, primary_key=True, index=True)
    record_type = Column(String)  # income, expense
    category = Column(String)
    amount = Column(Float)
    description = Column(Text)
    reference_no = Column(String)
    date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 對帳單模型
class Statement(Base):
    __tablename__ = "statements"

    id = Column(Integer, primary_key=True, index=True)
    statement_no = Column(String, unique=True, index=True)  # 對帳單號
    customer_id = Column(Integer, ForeignKey("customers.id"))  # 客戶ID
    customer_code = Column(String)  # 客戶代碼
    period_start = Column(Date)  # 對帳期間開始
    period_end = Column(Date)  # 對帳期間結束
    subtotal = Column(Float, default=0.0)  # 小計
    tax_rate = Column(Float, default=0.08)  # 稅率 (8%)
    tax_amount = Column(Float, default=0.0)  # 稅金
    total_amount = Column(Float, default=0.0)  # 總計
    status = Column(String, default="draft")  # draft, sent, confirmed
    customer_confirmed = Column(Boolean, default=False)  # 客戶確認
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 關聯
    customer = relationship("Customer")
    items = relationship("StatementItem", back_populates="statement")

# 對帳單項目模型
class StatementItem(Base):
    __tablename__ = "statement_items"

    id = Column(Integer, primary_key=True, index=True)
    statement_id = Column(Integer, ForeignKey("statements.id"))
    sequence_no = Column(Integer)  # 序號
    delivery_date = Column(Date)  # 送貨日期
    delivery_note_no = Column(String)  # 送貨單號
    product_name = Column(String)  # 品名
    product_name_vn = Column(String)  # 越南文品名
    specifications = Column(String)  # 規格
    unit = Column(String)  # 單位
    quantity = Column(Float)  # 數量
    unit_price = Column(Float)  # 單價
    amount = Column(Float)  # 金額
    notes = Column(Text)  # 備註
    created_at = Column(DateTime, default=datetime.now)

    # 關聯
    statement = relationship("Statement", back_populates="items")

# 成本分析模型
class CostAnalysis(Base):
    __tablename__ = "cost_analysis"

    id = Column(Integer, primary_key=True, index=True)
    quote_date = Column(Date)  # 報價時間/Ngày báo giá
    supplier_code = Column(String)  # 供應商代碼/Mã nhà cung cấp
    customer_name = Column(String)  # 客戶/Khách hàng
    product_code = Column(String)  # 編號/料號/Mã hàng
    product_name = Column(String)  # 品名/Tên hàng
    mold_number = Column(String)  # 刀模號/số khuôn
    design_number = Column(String)  # 圖稿/số bản in
    has_waterproof = Column(Boolean, default=False)  # 防水/chống thấm
    has_film = Column(Boolean, default=False)  # 過模/qua màng
    category = Column(String)  # 分類/Loại

    # 規格尺寸
    specifications = Column(String)  # 成品規格/Quy cách (mm)

    # 材質資訊
    paper_quality = Column(Integer)  # 材質克重/chất lượng giấy (gram)
    paper_code = Column(String)  # 材質代号/mã giấy
    paper_width = Column(Float)  # 门幅/KHỔ
    cut_width = Column(Float)  # 切宽/CẮT RỘNG (mm)
    cut_length = Column(Float)  # 切长/CẮT DÀI (mm)
    small_sheets = Column(Integer)  # 小张数/SỐ TẤM NHỎ
    large_sheets = Column(Integer)  # 大张数/SỐ TẤM LỚN
    cutting_sheets = Column(Integer)  # 开张数/SỐ DAO
    corrugated = Column(String)  # 瓦楞/SÓNG
    pressing_lines = Column(String)  # 压线/CÁN LẰN
    meters = Column(Float)  # 米数/SỐ MÉT (m)
    area = Column(Float)  # 平方数/DIỆN TÍCH (m²)

    # 價格計算
    cardboard_unit_price = Column(Float)  # 紙板單價/Đơn Giá (vnd/㎡)
    subtotal_before_tax = Column(Float)  # 小计/小计 (chưa tính thuế)
    moq = Column(Integer)  # MOQ
    labor_cost = Column(Float)  # 人工成本/Chi phí nhân công
    shipping_fee = Column(Float)  # 运输费/Phí vận chuyển
    film_cost = Column(Float)  # 過模/qua màng
    mold_cost = Column(Float)  # 刀模費/tiền khuôn
    additional_cost = Column(Float)  # 额外开支/Chi phí phát sinh
    total_cost = Column(Float)  # 總成本/Tổng chi phí
    unit_price = Column(Float)  # 單價/Đơn giá
    profit_amount = Column(Float)  # 利润金額/Lợi nhuận
    profit_percentage = Column(Float)  # 利润百分比/Lợi nhuận

    # 系統欄位
    status = Column(String, default="draft")  # draft, pending_approval, approved, rejected
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 核准相關欄位
    submitted_for_approval_at = Column(DateTime)  # 提交核准時間
    submitted_by = Column(String)  # 提交核准者
    approved_at = Column(DateTime)  # 核准時間
    approved_by = Column(String)  # 核准者
    approval_notes = Column(Text)  # 核准備註
    rejected_at = Column(DateTime)  # 拒絕時間
    rejected_by = Column(String)  # 拒絕者
    rejection_reason = Column(Text)  # 拒絕原因

# 生產安排模型
class ProductionArrangement(Base):
    __tablename__ = "production_arrangements"

    id = Column(Integer, primary_key=True, index=True)
    sequence_no = Column(Integer)  # STT 序號
    customer_name = Column(String)  # 客戶/Khách hàng
    order_code = Column(String)  # 訂單編號/MÃ ĐƠN ĐẶT HÀNG
    material_code = Column(String)  # 代号/Chất liệu
    cut_width = Column(Float)  # 切宽/Cắt rộng (mm)
    cut_length = Column(Float)  # 切长/Cắt dài (mm)
    small_sheets = Column(Integer)  # 小张数/Số tấm nhỏ
    corrugated = Column(String)  # 瓦楞/SÓNG
    pressing_lines = Column(String)  # 压线/CÁN LẰN
    product_name = Column(String)  # 產品名稱/Tên hàng hóa
    product_name_with_spec = Column(String)  # 產品名稱-規格/Tên hàng - quy cách
    ordered_quantity = Column(Integer)  # 訂单数量/SL khách đặt
    actual_quantity = Column(Integer)  # 實際數量/SL thực tế
    notes = Column(Text)  # 备注/Ghi chú

    # 生產狀態
    production_status = Column(String, default="pending")  # pending, in_progress, completed, cancelled
    priority = Column(String, default="normal")  # high, normal, low
    scheduled_date = Column(Date)  # 排程日期
    start_date = Column(Date)  # 開始日期
    completion_date = Column(Date)  # 完成日期

    # 系統欄位
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 訂單管理模型
class OrderManagement(Base):
    __tablename__ = "order_management"

    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String, unique=True, index=True)  # 訂單編號/số đơn dặt hàng
    customer_code = Column(String)  # 客戶代碼/Mã khách hàng
    order_received_date = Column(Date)  # 接單日期/Nhận đơn
    delivery_deadline = Column(Date)  # 指定送貨時間/Phải giao hàng
    product_name = Column(String)  # 產品名稱/Tên hàng hóa
    product_category = Column(String)  # 品項/Loại
    specifications = Column(String)  # 規格/Quy cách
    unit = Column(String)  # 單位/Đvt
    ordered_quantity = Column(Integer)  # 訂单数量/SL khách đặt
    paper_order_quantity = Column(Integer)  # 原物料採購/số đặt giấy
    actual_paper_quantity = Column(Integer)  # 紙板實際數量/SL giấy Thực tế
    printing_plate = Column(String)  # 印刷版/bảng in
    mold_number = Column(String)  # 刀模號/số khuôn
    production_status = Column(String, default="pending")  # 生產狀態/Tình trạng sản xuất
    unit_price = Column(Float)  # 單價/Đơn giá
    total_amount = Column(Float)  # 金額/Thành tiền
    delivered_quantity = Column(Integer, default=0)  # 交货数量/SL Giao hàng
    remaining_quantity = Column(Integer)  # 剩余订单总数/Tổng đơn còn lại
    delivery_note_number = Column(String)  # 送貨單號/phiếu giao hàng
    notes = Column(Text)  # 備註/Ghi chú

    # 訂單狀態
    order_status = Column(String, default="active")  # active, completed, cancelled
    priority = Column(String, default="normal")  # high, normal, low

    # 系統欄位
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 紙板單價模型
class CardboardPricing(Base):
    __tablename__ = "cardboard_pricing"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date)  # 時間/Ngày
    supplier_code = Column(String)  # 供應商代碼/Mã nhà cung cấp
    material_weight = Column(String)  # 材質克重/Chất lượng (g) - 支援複合格式如 120/90/90/90/120
    material_code = Column(String)  # 材質代號/Mã giấy
    unit_price = Column(Float)  # 價格/Đơn giá
    corrugated_type = Column(String)  # 瓦楞/SÓNG

    # 額外欄位
    currency = Column(String, default="VND")  # 貨幣單位
    status = Column(String, default="active")  # active, inactive
    effective_date = Column(Date)  # 生效日期
    expiry_date = Column(Date)  # 失效日期
    notes = Column(Text)  # 備註

    # 系統欄位
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 輔料單價模型
class AuxiliaryPricing(Base):
    __tablename__ = "auxiliary_pricing"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date)  # 時間/Ngày
    supplier_code = Column(String)  # 供應商代碼/Mã nhà cung cấp
    product_name_code = Column(String)  # 成品/代号/Tên hàng/ Mã hàng
    specifications = Column(String)  # 规格/Quy cách
    unit = Column(String)  # 单位/Đơn vị tính
    unit_price = Column(Float)  # 價格/Đơn giá

    # 額外欄位
    product_category = Column(String)  # 產品分類
    currency = Column(String, default="VND")  # 貨幣單位
    status = Column(String, default="active")  # active, inactive
    effective_date = Column(Date)  # 生效日期
    expiry_date = Column(Date)  # 失效日期
    minimum_order_quantity = Column(Integer)  # 最小訂購量
    notes = Column(Text)  # 備註

    # 系統欄位
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# 創建資料庫表格
def create_tables():
    Base.metadata.create_all(bind=engine)

# 初始化測試數據
def init_test_data():
    from passlib.context import CryptContext

    db = SessionLocal()

    # 檢查是否已有數據
    if db.query(InventoryItem).count() > 0:
        db.close()
        return

    # 創建密碼加密上下文
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    # 創建默認管理員用戶
    if db.query(User).count() == 0:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=pwd_context.hash("admin123"),
            full_name="系統管理員",
            role=UserRole.ADMIN,
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        print("已創建默認管理員用戶: admin / admin123")
    
    # 不再創建測試庫存項目，系統啟動時為空白狀態
    print("庫存系統初始化完成，為空白狀態")
    
    # 不再創建測試資料，系統啟動時為空白狀態
    print("客戶管理系統初始化完成，為空白狀態")

    db.close()
    print("ERP系統資料庫初始化完成，僅保留管理員帳號")