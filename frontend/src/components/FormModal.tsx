import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker, InputNumber, Row, Col } from 'antd';
import dayjs from 'dayjs';

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'date' | 'textarea' | 'email' | 'phone';
  required?: boolean;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  span?: number;
  placeholder?: string;
  disabled?: boolean;
}

interface FormModalProps {
  title: string;
  visible: boolean;
  onOk: (values: any) => void;
  onCancel: () => void;
  fields: FormField[];
  initialValues?: any;
  width?: number;
  loading?: boolean;
}

const FormModal: React.FC<FormModalProps> = ({
  title,
  visible,
  onOk,
  onCancel,
  fields,
  initialValues,
  width = 800,
  loading = false
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && initialValues) {
      // 處理日期欄位
      const processedValues = { ...initialValues };
      fields.forEach(field => {
        if (field.type === 'date' && processedValues[field.name]) {
          processedValues[field.name] = dayjs(processedValues[field.name]);
        }
      });
      form.setFieldsValue(processedValues);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, initialValues, form, fields]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 處理日期欄位
      const processedValues = { ...values };
      fields.forEach(field => {
        if (field.type === 'date' && processedValues[field.name]) {
          processedValues[field.name] = processedValues[field.name].format('YYYY-MM-DD');
        }
      });
      
      onOk(processedValues);
    } catch (error) {
      console.error('表單驗證失敗:', error);
    }
  };

  const renderField = (field: FormField) => {
    const commonProps = {
      placeholder: field.placeholder || `請輸入${field.label}`,
      disabled: field.disabled,
    };

    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={3} {...commonProps} />;
      case 'number':
        return <InputNumber style={{ width: '100%' }} {...commonProps} />;
      case 'select':
        return (
          <Select {...commonProps} allowClear>
            {field.options?.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case 'date':
        return <DatePicker style={{ width: '100%' }} {...commonProps} />;
      case 'email':
        return <Input type="email" {...commonProps} />;
      case 'phone':
        return <Input {...commonProps} />;
      default:
        return <Input {...commonProps} />;
    }
  };

  const getDefaultRules = (field: FormField) => {
    const rules = [...(field.rules || [])];
    
    if (field.required) {
      rules.unshift({
        required: true,
        message: `請輸入${field.label}`
      });
    }

    if (field.type === 'email') {
      rules.push({
        type: 'email',
        message: '請輸入有效的電子郵件地址'
      });
    }

    return rules;
  };

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={width}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Row gutter={16}>
          {fields.map((field) => (
            <Col span={field.span || 12} key={field.name}>
              <Form.Item
                name={field.name}
                label={field.label}
                rules={getDefaultRules(field)}
              >
                {renderField(field)}
              </Form.Item>
            </Col>
          ))}
        </Row>
      </Form>
    </Modal>
  );
};

export default FormModal;
