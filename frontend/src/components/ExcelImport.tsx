import React, { useState } from 'react';
import {
  Modal,
  Upload,
  Button,
  Steps,
  Table,
  message,
  Alert,
  Progress,
  Space,
  Select,
  Card,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import * as XLSX from 'xlsx';

const { Step } = Steps;
const { Option } = Select;
const { Text, Title } = Typography;

interface ExcelImportProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (data: any[]) => void;
  type: 'product' | 'order'; // 匯入類型
}

interface ImportData {
  [key: string]: any;
}

const ExcelImport: React.FC<ExcelImportProps> = ({
  visible,
  onCancel,
  onSuccess,
  type,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileData, setFileData] = useState<ImportData[]>([]);
  const [selectedSheet, setSelectedSheet] = useState<string>('');
  const [sheetNames, setSheetNames] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<{ [key: string]: string }>({});
  const [validationResults, setValidationResults] = useState<any[]>([]);
  const [importing, setImporting] = useState(false);

  // 產品欄位對應
  const productFieldMapping = {
    'quoteDate': '報價時間',
    'customerName': '客戶',
    'productCode': '編號/料號',
    'category': '分類',
    'finishedSize': '成品規格',
    'materialWeight': '材質克重',
    'cutWidth': '切寬',
    'cutLength': '切長',
    'moq': 'MOQ',
    'areaSqm': '平方數',
    'paperUnitPrice': '紙板單價',
    'laborCost': '人工成本',
    'transportFee': '運輸費',
    'totalCost': '總成本',
    'unitPrice': '單價',
    'profitAmount': '利潤',
    'profitMargin': '利潤率',
  };

  // 訂單欄位對應
  const orderFieldMapping = {
    'orderNumber': '訂單編號',
    'customerCode': '客戶',
    'companyFullName': '公司全稱',
    'orderDate': '接單日期',
    'deliveryDate': '指定送貨時間',
    'productName': '產品名稱',
    'specifications': '規格',
    'unit': '單位',
    'orderQuantity': '訂單數量',
    'paperOrderQuantity': '原物料採購',
    'actualPaperQuantity': '紙板實際數量',
    'moldNumber': '刀模號',
    'productionStatus': '生產狀態',
    'unitPrice': '單價',
    'totalAmount': '金額',
    'deliveryQuantity': '交貨數量',
    'remainingQuantity': '剩餘數量',
    'deliveryNoteNumber': '送貨單號',
    'notes': '備註',
  };

  const currentFieldMapping = type === 'product' ? productFieldMapping : orderFieldMapping;

  // 處理檔案上傳
  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        setSheetNames(workbook.SheetNames);
        setCurrentStep(1);
        
        if (workbook.SheetNames.length === 1) {
          // 如果只有一個工作表，自動選擇
          handleSheetSelect(workbook.SheetNames[0], workbook);
        }
      } catch (error) {
        message.error('檔案讀取失敗，請確認檔案格式正確');
      }
    };
    reader.readAsArrayBuffer(file);
    return false; // 阻止自動上傳
  };

  // 處理工作表選擇
  const handleSheetSelect = (sheetName: string, workbook?: XLSX.WorkBook) => {
    setSelectedSheet(sheetName);
    
    if (workbook) {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // 轉換為物件格式
      const headers = jsonData[0] as string[];
      const rows = jsonData.slice(1) as any[][];
      
      const formattedData = rows.map((row, index) => {
        const obj: ImportData = { _rowIndex: index + 2 }; // Excel 行號從2開始
        headers.forEach((header, colIndex) => {
          obj[header] = row[colIndex];
        });
        return obj;
      }).filter(row => Object.keys(row).length > 1); // 過濾空行
      
      setFileData(formattedData);
      setCurrentStep(2);
    }
  };

  // 處理欄位對應
  const handleColumnMapping = (field: string, excelColumn: string) => {
    setColumnMapping(prev => ({
      ...prev,
      [field]: excelColumn,
    }));
  };

  // 驗證資料
  const validateData = () => {
    const results = fileData.map((row, index) => {
      const errors: string[] = [];
      const warnings: string[] = [];
      
      // 必填欄位檢查
      const requiredFields = type === 'product' 
        ? ['productCode', 'customerName', 'category']
        : ['orderNumber', 'customerCode', 'productName'];
      
      requiredFields.forEach(field => {
        const excelColumn = columnMapping[field];
        if (!excelColumn || !row[excelColumn]) {
          errors.push(`${currentFieldMapping[field]} 為必填欄位`);
        }
      });

      // 數值欄位檢查
      const numericFields = type === 'product'
        ? ['cutWidth', 'cutLength', 'moq', 'areaSqm', 'paperUnitPrice', 'laborCost', 'totalCost', 'unitPrice']
        : ['orderQuantity', 'unitPrice', 'totalAmount', 'deliveryQuantity'];
      
      numericFields.forEach(field => {
        const excelColumn = columnMapping[field];
        if (excelColumn && row[excelColumn] && isNaN(Number(row[excelColumn]))) {
          errors.push(`${currentFieldMapping[field]} 必須為數值`);
        }
      });

      return {
        rowIndex: row._rowIndex,
        status: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'success',
        errors,
        warnings,
        data: row,
      };
    });

    setValidationResults(results);
    setCurrentStep(3);
  };

  // 執行匯入
  const handleImport = async () => {
    setImporting(true);
    
    try {
      // 過濾出成功的資料
      const validData = validationResults
        .filter(result => result.status !== 'error')
        .map(result => {
          const mappedData: any = {};
          Object.keys(columnMapping).forEach(field => {
            const excelColumn = columnMapping[field];
            if (excelColumn) {
              mappedData[field] = result.data[excelColumn];
            }
          });
          return mappedData;
        });

      // 這裡應該調用 API 將資料匯入到後端
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模擬 API 調用
      
      message.success(`成功匯入 ${validData.length} 筆資料`);
      onSuccess(validData);
      handleReset();
    } catch (error) {
      message.error('匯入失敗，請稍後再試');
    } finally {
      setImporting(false);
    }
  };

  // 重置狀態
  const handleReset = () => {
    setCurrentStep(0);
    setFileData([]);
    setSelectedSheet('');
    setSheetNames([]);
    setColumnMapping({});
    setValidationResults([]);
    setImporting(false);
  };

  // 取消匯入
  const handleCancel = () => {
    handleReset();
    onCancel();
  };

  // 驗證結果表格欄位
  const validationColumns = [
    {
      title: '行號',
      dataIndex: 'rowIndex',
      key: 'rowIndex',
      width: 80,
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const config = {
          success: { color: 'green', icon: <CheckCircleOutlined />, text: '成功' },
          warning: { color: 'orange', icon: <ExclamationCircleOutlined />, text: '警告' },
          error: { color: 'red', icon: <ExclamationCircleOutlined />, text: '錯誤' },
        };
        const { color, icon, text } = config[status as keyof typeof config];
        return <Text type={status === 'error' ? 'danger' : undefined}>{icon} {text}</Text>;
      },
    },
    {
      title: '錯誤訊息',
      dataIndex: 'errors',
      key: 'errors',
      render: (errors: string[]) => errors.join(', ') || '-',
    },
  ];

  return (
    <Modal
      title={`匯入 ${type === 'product' ? '商品資料' : '訂單資料'}`}
      visible={visible}
      onCancel={handleCancel}
      width={1000}
      footer={null}
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="上傳檔案" />
        <Step title="選擇工作表" />
        <Step title="欄位對應" />
        <Step title="驗證資料" />
        <Step title="完成匯入" />
      </Steps>

      {currentStep === 0 && (
        <Card>
          <Upload.Dragger
            beforeUpload={handleFileUpload}
            accept=".xlsx,.xls"
            showUploadList={false}
          >
            <p className="ant-upload-drag-icon">
              <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">點擊或拖拽 Excel 檔案到此區域</p>
            <p className="ant-upload-hint">支援 .xlsx 和 .xls 格式</p>
          </Upload.Dragger>
        </Card>
      )}

      {currentStep === 1 && (
        <Card title="選擇工作表">
          <Select
            placeholder="請選擇要匯入的工作表"
            style={{ width: '100%', marginBottom: 16 }}
            value={selectedSheet}
            onChange={(value) => handleSheetSelect(value)}
          >
            {sheetNames.map(name => (
              <Option key={name} value={name}>{name}</Option>
            ))}
          </Select>
          
          {selectedSheet && (
            <Alert
              message={`已選擇工作表: ${selectedSheet}`}
              description={`共找到 ${fileData.length} 筆資料`}
              type="info"
              showIcon
            />
          )}
        </Card>
      )}

      {currentStep === 2 && (
        <Card title="欄位對應">
          <Row gutter={[16, 16]}>
            {Object.keys(currentFieldMapping).map(field => (
              <Col span={12} key={field}>
                <Space style={{ width: '100%' }}>
                  <Text strong style={{ width: 120 }}>
                    {currentFieldMapping[field]}:
                  </Text>
                  <Select
                    placeholder="選擇對應的 Excel 欄位"
                    style={{ flex: 1 }}
                    value={columnMapping[field]}
                    onChange={(value) => handleColumnMapping(field, value)}
                    allowClear
                  >
                    {fileData.length > 0 && Object.keys(fileData[0])
                      .filter(key => key !== '_rowIndex')
                      .map(column => (
                        <Option key={column} value={column}>{column}</Option>
                      ))}
                  </Select>
                </Space>
              </Col>
            ))}
          </Row>
          
          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Button type="primary" onClick={validateData}>
              下一步：驗證資料
            </Button>
          </div>
        </Card>
      )}

      {currentStep === 3 && (
        <Card title="資料驗證結果">
          <Alert
            message={`驗證完成`}
            description={`共 ${validationResults.length} 筆資料，其中 ${validationResults.filter(r => r.status === 'success').length} 筆成功，${validationResults.filter(r => r.status === 'error').length} 筆錯誤`}
            type={validationResults.some(r => r.status === 'error') ? 'warning' : 'success'}
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Table
            columns={validationColumns}
            dataSource={validationResults}
            rowKey="rowIndex"
            pagination={{ pageSize: 10 }}
            size="small"
          />
          
          <div style={{ marginTop: 24, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setCurrentStep(2)}>
                上一步
              </Button>
              <Button 
                type="primary" 
                onClick={handleImport}
                loading={importing}
                disabled={validationResults.filter(r => r.status !== 'error').length === 0}
              >
                開始匯入
              </Button>
            </Space>
          </div>
        </Card>
      )}

      {importing && (
        <Card>
          <div style={{ textAlign: 'center' }}>
            <Progress type="circle" percent={50} />
            <div style={{ marginTop: 16 }}>
              <Text>正在匯入資料，請稍候...</Text>
            </div>
          </div>
        </Card>
      )}
    </Modal>
  );
};

export default ExcelImport;
