import React from 'react';
import { Row, Col, Input, Select, DatePicker, Button, Space } from 'antd';
import { SearchOutlined, ClearOutlined, PlusOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

interface FilterField {
  key: string;
  label: string;
  type: 'search' | 'select' | 'dateRange';
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  span?: number;
}

interface SearchFiltersProps {
  fields: FilterField[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
  filters: Record<string, any>;
  onFilterChange: (key: string, value: any) => void;
  onClear: () => void;
  onAdd?: () => void;
  addButtonText?: string;
  showAddButton?: boolean;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  fields,
  searchTerm,
  onSearchChange,
  filters,
  onFilterChange,
  onClear,
  onAdd,
  addButtonText = '新增',
  showAddButton = true
}) => {
  const renderField = (field: FilterField) => {
    const commonProps = {
      placeholder: field.placeholder || field.label,
      style: { width: '100%' },
      allowClear: true,
    };

    switch (field.type) {
      case 'search':
        return (
          <Input
            {...commonProps}
            prefix={<SearchOutlined />}
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        );
      case 'select':
        return (
          <Select
            {...commonProps}
            value={filters[field.key]}
            onChange={(value) => onFilterChange(field.key, value)}
          >
            {field.options?.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case 'dateRange':
        return (
          <RangePicker
            {...commonProps}
            value={filters[field.key]}
            onChange={(dates) => onFilterChange(field.key, dates)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      {fields.map((field) => (
        <Col span={field.span || 6} key={field.key}>
          {renderField(field)}
        </Col>
      ))}
      <Col span={6}>
        <Space>
          <Button
            icon={<ClearOutlined />}
            onClick={onClear}
          >
            清除
          </Button>
          {showAddButton && onAdd && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onAdd}
            >
              {addButtonText}
            </Button>
          )}
        </Space>
      </Col>
    </Row>
  );
};

export default SearchFilters;
