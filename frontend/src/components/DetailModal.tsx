import React from 'react';
import { Modal, Descriptions, Tag, Space } from 'antd';
import { formatDate, formatCurrency } from '../hooks/useLocalStorage';

interface DetailField {
  label: string;
  key: string;
  type?: 'text' | 'date' | 'currency' | 'tag' | 'custom';
  render?: (value: any, record: any) => React.ReactNode;
  span?: number;
}

interface DetailModalProps {
  title: string;
  visible: boolean;
  onClose: () => void;
  data: any;
  fields: DetailField[];
  width?: number;
}

const DetailModal: React.FC<DetailModalProps> = ({
  title,
  visible,
  onClose,
  data,
  fields,
  width = 800
}) => {
  if (!data) return null;

  const renderValue = (field: DetailField, value: any) => {
    if (field.render) {
      return field.render(value, data);
    }

    switch (field.type) {
      case 'date':
        return value ? formatDate(value) : '-';
      case 'currency':
        return typeof value === 'number' ? formatCurrency(value) : '-';
      case 'tag':
        return value ? <Tag>{value}</Tag> : '-';
      default:
        return value || '-';
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={width}
    >
      <Descriptions bordered column={2} size="small">
        {fields.map((field) => (
          <Descriptions.Item
            key={field.key}
            label={field.label}
            span={field.span || 1}
          >
            {renderValue(field, data[field.key])}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </Modal>
  );
};

export default DetailModal;
