import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Button,
  Dropdown,
  Space,
  Typography,
  theme
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  ShopOutlined,
  DollarOutlined,
  TeamOutlined,
  AppstoreOutlined,
  ScheduleOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  GlobalOutlined,
  BarChartOutlined,
  FileTextOutlined,
  OrderedListOutlined,
  PrinterOutlined,
  ToolOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  FileProtectOutlined,
  ImportOutlined,
  ExportOutlined,
  UndoOutlined,
  CalendarOutlined,
  CreditCardOutlined,
  ReconciliationOutlined,
  MoneyCollectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

const Layout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 選單項目 - 根據新的側邊欄結構
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: t('navigation.dashboard'),
    },
    {
      key: 'business',
      icon: <ShoppingCartOutlined />,
      label: t('navigation.businessManagement'),
      children: [
        {
          key: 'suppliers',
          icon: <TeamOutlined />,
          label: t('navigation.suppliers'),
          children: [
            {
              key: '/supplier-data',
              icon: <UserOutlined />,
              label: t('navigation.supplierData'),
            },
            {
              key: '/cardboard-pricing',
              icon: <FileTextOutlined />,
              label: t('navigation.cardboardPricing'),
            },
            {
              key: '/auxiliary-pricing',
              icon: <FileTextOutlined />,
              label: t('navigation.auxiliaryPricing'),
            },
          ],
        },
        {
          key: '/customer-data',
          icon: <UserOutlined />,
          label: t('navigation.customerData'),
        },
        {
          key: '/cost-analysis',
          icon: <BarChartOutlined />,
          label: t('navigation.costAnalysis'),
        },
        {
          key: '/order-management',
          icon: <OrderedListOutlined />,
          label: t('navigation.orderManagement'),
        },
      ],
    },
    {
      key: 'production',
      icon: <ScheduleOutlined />,
      label: t('navigation.productionManagement'),
      children: [
        {
          key: '/production-schedule',
          icon: <CalendarOutlined />,
          label: t('navigation.productionSchedule'),
        },
        {
          key: '/printing-machine-1',
          icon: <PrinterOutlined />,
          label: t('navigation.printingMachine1'),
        },
        {
          key: '/printing-machine-2',
          icon: <PrinterOutlined />,
          label: t('navigation.printingMachine2'),
        },
        {
          key: '/laminating-machine',
          icon: <ToolOutlined />,
          label: t('navigation.laminatingMachine'),
        },
        {
          key: '/stapling-machine',
          icon: <ToolOutlined />,
          label: t('navigation.staplingMachine'),
        },
        {
          key: '/creasing',
          icon: <ToolOutlined />,
          label: t('navigation.creasing'),
        },
        {
          key: '/laminating',
          icon: <ToolOutlined />,
          label: t('navigation.laminating'),
        },
        {
          key: '/paper-cutting',
          icon: <ToolOutlined />,
          label: t('navigation.paperCutting'),
        },
        {
          key: '/handwork-1',
          icon: <UserOutlined />,
          label: t('navigation.handwork1'),
        },
        {
          key: '/handwork-2',
          icon: <UserOutlined />,
          label: t('navigation.handwork2'),
        },
        {
          key: '/handwork-3',
          icon: <UserOutlined />,
          label: t('navigation.handwork3'),
        },
      ],
    },
    {
      key: 'quality',
      icon: <SafetyOutlined />,
      label: t('navigation.qualityManagement'),
      children: [
        {
          key: '/kpi',
          icon: <BarChartOutlined />,
          label: t('navigation.kpi'),
        },
        {
          key: '/quality-inspection',
          icon: <CheckCircleOutlined />,
          label: t('navigation.qualityInspection'),
        },
        {
          key: '/reports',
          icon: <FileProtectOutlined />,
          label: t('navigation.reports'),
        },
      ],
    },
    {
      key: 'warehouse',
      icon: <InboxOutlined />,
      label: t('navigation.warehouseManagement'),
      children: [
        {
          key: '/incoming',
          icon: <ImportOutlined />,
          label: t('navigation.incoming'),
        },
        {
          key: '/outgoing',
          icon: <ExportOutlined />,
          label: t('navigation.outgoing'),
        },
        {
          key: '/returns',
          icon: <UndoOutlined />,
          label: t('navigation.returns'),
        },
        {
          key: '/inventory',
          icon: <ShopOutlined />,
          label: t('navigation.inventory'),
        },
      ],
    },
    {
      key: 'hr',
      icon: <TeamOutlined />,
      label: t('navigation.hr'),
      children: [
        {
          key: '/employee-data',
          icon: <UserOutlined />,
          label: t('navigation.employeeData'),
        },
        {
          key: '/attendance',
          icon: <CalendarOutlined />,
          label: t('navigation.attendance'),
        },
        {
          key: '/payroll',
          icon: <DollarOutlined />,
          label: t('navigation.payroll'),
        },
      ],
    },
    {
      key: 'finance',
      icon: <DollarOutlined />,
      label: t('navigation.finance'),
      children: [
        {
          key: '/ledger',
          icon: <FileTextOutlined />,
          label: t('navigation.ledger'),
        },
        {
          key: '/statements',
          icon: <ReconciliationOutlined />,
          label: t('navigation.statements'),
        },
        {
          key: '/accounts-receivable',
          icon: <MoneyCollectOutlined />,
          label: t('navigation.accountsReceivable'),
        },
        {
          key: '/accounts-payable',
          icon: <CreditCardOutlined />,
          label: t('navigation.accountsPayable'),
        },
      ],
    },
  ];

  // 語言切換選單
  const languageMenu = {
    items: [
      {
        key: 'zh-TW',
        label: '繁體中文',
        onClick: () => i18n.changeLanguage('zh-TW'),
      },
      {
        key: 'vi-VN',
        label: 'Tiếng Việt',
        onClick: () => i18n.changeLanguage('vi-VN'),
      },
    ],
  };

  // 使用者選單
  const userMenu = {
    items: [
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: t('navigation.settings'),
        onClick: () => navigate('/settings'),
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: t('navigation.logout'),
        onClick: () => {
          // TODO: 實際登出邏輯
          navigate('/login');
        },
      },
    ],
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={200}
        collapsedWidth={80}
        breakpoint="lg"
        onBreakpoint={(broken) => {
          setCollapsed(broken);
        }}
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? t('common.erpShort') : `${t('dashboard.companyNameShort')} ${t('common.erp')}`}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <AntLayout>
        <Header style={{ 
          padding: 0, 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingRight: 24
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          <Space>
            <Dropdown menu={languageMenu} placement="bottomRight">
              <Button type="text" icon={<GlobalOutlined />}>
                {i18n.language === 'vi-VN' ? t('common.vietnamese') : t('common.chinese')}
              </Button>
            </Dropdown>
            <Dropdown menu={userMenu} placement="bottomRight">
              <Button type="text" icon={<UserOutlined />}>
                {t('common.admin')}
              </Button>
            </Dropdown>
          </Space>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 'calc(100vh - 112px)',
            maxHeight: 'calc(100vh - 112px)',
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
            overflow: 'auto',
          }}
        >
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
