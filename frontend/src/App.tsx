import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { useTranslation } from 'react-i18next';
import zhTW from 'antd/locale/zh_TW';
import viVN from 'antd/locale/vi_VN';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import CustomerOrders from './pages/CustomerOrders';
import MaterialOrders from './pages/MaterialOrders';
import Inventory from './pages/Inventory';
import CustomerAnalysis from './pages/CustomerAnalysis';
import Settings from './pages/Settings';
import Finance from './pages/Finance';
import HRPayroll from './pages/HRPayroll';
import PlaceholderPage from './pages/PlaceholderPage';
import SupplierData from './pages/SupplierData';
import CustomerData from './pages/CustomerData';
import Statements from './pages/Statements';
import CostAnalysis from './pages/CostAnalysis';
import ProductionArrangement from './pages/ProductionArrangement';
import CardboardPricing from './pages/CardboardPricing';
import AuxiliaryPricing from './pages/AuxiliaryPricing';
import ProductData from './pages/ProductData';
import CustomerManagement from './pages/CustomerManagement';
import ProductionSchedule from './pages/ProductionSchedule';
import ProductQuotation from './pages/ProductQuotation';
import OrderManagement from './pages/OrderManagement';
import './App.css';

function App() {
  const { i18n } = useTranslation();

  // 根據當前語言設定 Ant Design 的語言包
  const getAntdLocale = () => {
    switch (i18n.language) {
      case 'vi-VN':
        return viVN;
      case 'zh-TW':
      default:
        return zhTW;
    }
  };

  return (
    <ConfigProvider locale={getAntdLocale()}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* 業務管理 */}
            <Route path="supplier-data" element={<SupplierData />} />
            <Route path="cardboard-pricing" element={<CardboardPricing />} />
            <Route path="auxiliary-pricing" element={<AuxiliaryPricing />} />
            <Route path="customer-data" element={<CustomerData />} />
            <Route path="cost-analysis" element={<CostAnalysis />} />
            <Route path="order-management" element={<OrderManagement />} />

            {/* 生產管理 */}
            <Route path="production-schedule" element={<ProductionArrangement />} />
            <Route path="printing-machine-1" element={<PlaceholderPage title="印刷機1" />} />
            <Route path="printing-machine-2" element={<PlaceholderPage title="印刷機2" />} />
            <Route path="laminating-machine" element={<PlaceholderPage title="貼合機" />} />
            <Route path="stapling-machine" element={<PlaceholderPage title="打钉機" />} />
            <Route path="creasing" element={<PlaceholderPage title="壓痕機" />} />
            <Route path="laminating" element={<PlaceholderPage title="覆膜機" />} />
            <Route path="paper-cutting" element={<PlaceholderPage title="分紙機" />} />
            <Route path="handwork-1" element={<PlaceholderPage title="手工區1" />} />
            <Route path="handwork-2" element={<PlaceholderPage title="手工區2" />} />
            <Route path="handwork-3" element={<PlaceholderPage title="手工區3" />} />

            {/* 品質管理 */}
            <Route path="kpi" element={<PlaceholderPage title="KPI" />} />
            <Route path="quality-inspection" element={<PlaceholderPage title="品檢" />} />
            <Route path="reports" element={<PlaceholderPage title="報告" />} />

            {/* 倉庫管理 */}
            <Route path="incoming" element={<PlaceholderPage title="進貨" />} />
            <Route path="outgoing" element={<PlaceholderPage title="出貨" />} />
            <Route path="returns" element={<PlaceholderPage title="退貨" />} />
            <Route path="inventory" element={<Inventory />} />

            {/* 人事 */}
            <Route path="employee-data" element={<PlaceholderPage title="員工資料" />} />
            <Route path="attendance" element={<PlaceholderPage title="考勤" />} />
            <Route path="payroll" element={<HRPayroll />} />

            {/* 財務 */}
            <Route path="ledger" element={<PlaceholderPage title="流水帳" />} />
            <Route path="statements" element={<Statements />} />
            <Route path="accounts-receivable" element={<PlaceholderPage title="應收帳款" />} />
            <Route path="accounts-payable" element={<PlaceholderPage title="應付帳款" />} />

            {/* 保留舊路由以防直接訪問 */}
            <Route path="customer-orders" element={<CustomerOrders />} />
            <Route path="material-orders" element={<MaterialOrders />} />
            <Route path="customer-analysis" element={<CustomerAnalysis />} />
            <Route path="finance" element={<Finance />} />
            <Route path="hr-payroll" element={<HRPayroll />} />
            <Route path="settings" element={<Settings />} />
          </Route>
        </Routes>
      </Router>
    </ConfigProvider>
  );
}

export default App;
