import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';

export interface AuxiliaryPricing {
  id: number;
  date?: string;
  supplier_code?: string;
  product_name_code?: string;
  specifications?: string;
  unit?: string;
  unit_price?: number;
  product_category?: string;
  currency: string;
  status: string;
  effective_date?: string;
  expiry_date?: string;
  minimum_order_quantity?: number;
  notes?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface AuxiliaryPricingCreate {
  date?: string;
  supplier_code?: string;
  product_name_code?: string;
  specifications?: string;
  unit?: string;
  unit_price?: number;
  product_category?: string;
  currency?: string;
  status?: string;
  effective_date?: string;
  expiry_date?: string;
  minimum_order_quantity?: number;
  notes?: string;
  created_by?: string;
}

export interface AuxiliaryPricingUpdate extends Partial<AuxiliaryPricingCreate> {}

export interface PaginatedAuxiliaryPricingResponse {
  data: AuxiliaryPricing[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface AuxiliaryPricingQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  supplier_code?: string;
  product_category?: string;
  unit?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
}

export interface AuxiliaryPricingSummary {
  total_records: number;
  active_records: number;
  inactive_records: number;
  supplier_count: number;
  category_count: number;
  average_price: number;
}

export interface PriceComparison {
  product_name_code: string;
  price_count: number;
  min_price: number;
  max_price: number;
  avg_price: number;
  prices: AuxiliaryPricing[];
}

class AuxiliaryPricingAPI {
  private baseURL = `${API_BASE_URL}/api/auxiliary-pricing`;

  async getAuxiliaryPricing(params: AuxiliaryPricingQueryParams = {}): Promise<{ data: PaginatedAuxiliaryPricingResponse }> {
    const response = await axios.get(this.baseURL, { params });
    return response;
  }

  async getAuxiliaryPricingItem(id: number): Promise<{ data: AuxiliaryPricing }> {
    const response = await axios.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createAuxiliaryPricing(pricing: AuxiliaryPricingCreate): Promise<{ data: AuxiliaryPricing }> {
    const response = await axios.post(this.baseURL, pricing);
    return response;
  }

  async updateAuxiliaryPricing(id: number, pricing: AuxiliaryPricingUpdate): Promise<{ data: AuxiliaryPricing }> {
    const response = await axios.put(`${this.baseURL}/${id}`, pricing);
    return response;
  }

  async deleteAuxiliaryPricing(id: number): Promise<{ data: { message: string } }> {
    const response = await axios.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getSuppliersList(): Promise<{ data: string[] }> {
    const response = await axios.get(`${this.baseURL}/suppliers/list`);
    return response;
  }

  async getCategoriesList(): Promise<{ data: string[] }> {
    const response = await axios.get(`${this.baseURL}/categories/list`);
    return response;
  }

  async getUnitsList(): Promise<{ data: string[] }> {
    const response = await axios.get(`${this.baseURL}/units/list`);
    return response;
  }

  async getPricingSummary(): Promise<{ data: AuxiliaryPricingSummary }> {
    const response = await axios.get(`${this.baseURL}/dashboard/summary`);
    return response;
  }

  async batchUpdateStatus(pricingIds: number[], newStatus: string): Promise<{ data: { message: string } }> {
    const response = await axios.post(`${this.baseURL}/batch-update-status`, {
      pricing_ids: pricingIds,
      new_status: newStatus
    });
    return response;
  }

  async searchByProduct(productNameCode: string, supplierCode?: string, unit?: string): Promise<{ data: AuxiliaryPricing[] }> {
    const params = {
      product_name_code: productNameCode,
      ...(supplierCode && { supplier_code: supplierCode }),
      ...(unit && { unit: unit })
    };
    const response = await axios.get(`${this.baseURL}/search/by-product`, { params });
    return response;
  }

  async getPriceComparison(productNameCode: string): Promise<{ data: PriceComparison }> {
    const response = await axios.get(`${this.baseURL}/price-comparison/${productNameCode}`);
    return response;
  }
}

export const auxiliaryPricingAPI = new AuxiliaryPricingAPI();
