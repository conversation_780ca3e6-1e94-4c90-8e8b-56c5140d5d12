import api from './api';

class ProductDataAPI {
  private baseURL = '/api/product-data';

  async getProducts(params: any = {}): Promise<any> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getProduct(id: number): Promise<any> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createProduct(product: any): Promise<any> {
    const response = await api.post(this.baseURL, product);
    return response;
  }

  async updateProduct(id: number, product: any): Promise<any> {
    const response = await api.put(`${this.baseURL}/${id}`, product);
    return response;
  }

  async deleteProduct(id: number): Promise<any> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async generateProductCode(): Promise<any> {
    const response = await api.get(`${this.baseURL}/generate-code`);
    return response;
  }

  async getCategoriesList(): Promise<any> {
    const response = await api.get(`${this.baseURL}/categories/list`);
    return response;
  }

  async getSuppliersList(): Promise<any> {
    const response = await api.get(`${this.baseURL}/suppliers/list`);
    return response;
  }

  async getCustomersList(): Promise<any> {
    const response = await api.get(`${this.baseURL}/customers/list`);
    return response;
  }

  async getProductSummary(): Promise<any> {
    const response = await api.get(`${this.baseURL}/dashboard/summary`);
    return response;
  }
}

export const productDataAPI = new ProductDataAPI();