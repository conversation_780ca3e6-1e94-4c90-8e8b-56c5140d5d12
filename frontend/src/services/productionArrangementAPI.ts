import api from './api';

export interface ProductionArrangement {
  id: number;
  sequence_no?: number;
  customer_name?: string;
  order_code?: string;
  material_code?: string;
  cut_width?: number;
  cut_length?: number;
  small_sheets?: number;
  corrugated?: string;
  pressing_lines?: string;
  product_name?: string;
  product_name_with_spec?: string;
  ordered_quantity?: number;
  actual_quantity?: number;
  notes?: string;
  production_status: string;
  priority: string;
  scheduled_date?: string;
  start_date?: string;
  completion_date?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductionArrangementCreate {
  sequence_no?: number;
  customer_name?: string;
  order_code?: string;
  material_code?: string;
  cut_width?: number;
  cut_length?: number;
  small_sheets?: number;
  corrugated?: string;
  pressing_lines?: string;
  product_name?: string;
  product_name_with_spec?: string;
  ordered_quantity?: number;
  actual_quantity?: number;
  notes?: string;
  production_status?: string;
  priority?: string;
  scheduled_date?: string;
  start_date?: string;
  completion_date?: string;
  created_by?: string;
}

export interface ProductionArrangementUpdate extends ProductionArrangementCreate {}

export interface PaginatedProductionArrangementResponse {
  data: ProductionArrangement[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ProductionArrangementQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  production_status?: string;
  priority?: string;
  customer_name?: string;
  scheduled_date?: string;
}

export interface ProductionSummary {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  completion_rate: number;
}

export interface CustomerOption {
  id: number;
  customer_code: string;
  company_name: string;
  contact_person: string;
  display_name: string;
}

class ProductionArrangementAPI {
  private baseURL = '/api/production-arrangement';

  async getProductionArrangements(params: ProductionArrangementQueryParams = {}): Promise<{ data: PaginatedProductionArrangementResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getProductionArrangement(id: number): Promise<{ data: ProductionArrangement }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createProductionArrangement(arrangement: ProductionArrangementCreate): Promise<{ data: ProductionArrangement }> {
    const response = await api.post(this.baseURL, arrangement);
    return response;
  }

  async updateProductionArrangement(id: number, arrangement: ProductionArrangementUpdate): Promise<{ data: ProductionArrangement }> {
    const response = await api.put(`${this.baseURL}/${id}`, arrangement);
    return response;
  }

  async deleteProductionArrangement(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getCustomersList(): Promise<{ data: CustomerOption[] }> {
    const response = await api.get(`${this.baseURL}/customers/list`);
    return response;
  }

  async getMaterialsList(): Promise<{ data: string[] }> {
    const response = await api.get(`${this.baseURL}/materials/list`);
    return response;
  }

  async startProduction(id: number): Promise<{ data: ProductionArrangement }> {
    const response = await api.post(`${this.baseURL}/${id}/start`);
    return response;
  }

  async completeProduction(id: number, actualQuantity?: number): Promise<{ data: ProductionArrangement }> {
    const response = await api.post(`${this.baseURL}/${id}/complete`, {
      actual_quantity: actualQuantity
    });
    return response;
  }

  async batchUpdateSequence(updates: Array<{ id: number; sequence_no: number }>): Promise<{ data: { message: string } }> {
    const response = await api.post(`${this.baseURL}/batch-update-sequence`, updates);
    return response;
  }

  async getProductionSummary(): Promise<{ data: ProductionSummary }> {
    const response = await api.get(`${this.baseURL}/dashboard/summary`);
    return response;
  }
}

export const productionArrangementAPI = new ProductionArrangementAPI();
