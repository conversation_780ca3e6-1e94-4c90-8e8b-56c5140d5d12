import api from './api';

export interface OrderManagement {
  id: number;
  order_number: string;
  customer_code?: string;
  order_received_date?: string;
  delivery_deadline?: string;
  product_name?: string;
  product_category?: string;
  specifications?: string;
  unit: string;
  ordered_quantity?: number;
  paper_order_quantity?: number;
  actual_paper_quantity?: number;
  printing_plate?: string;
  mold_number?: string;
  production_status: string;
  unit_price?: number;
  total_amount?: number;
  delivered_quantity: number;
  remaining_quantity?: number;
  delivery_note_number?: string;
  notes?: string;
  order_status: string;
  priority: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderManagementCreate {
  order_number: string;
  customer_code?: string;
  order_received_date?: string;
  delivery_deadline?: string;
  product_name?: string;
  product_category?: string;
  specifications?: string;
  unit?: string;
  ordered_quantity?: number;
  paper_order_quantity?: number;
  actual_paper_quantity?: number;
  printing_plate?: string;
  mold_number?: string;
  production_status?: string;
  unit_price?: number;
  total_amount?: number;
  delivered_quantity?: number;
  remaining_quantity?: number;
  delivery_note_number?: string;
  notes?: string;
  order_status?: string;
  priority?: string;
  created_by?: string;
}

export interface OrderManagementUpdate extends Partial<OrderManagementCreate> {}

export interface PaginatedOrderManagementResponse {
  data: OrderManagement[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface OrderManagementQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  order_status?: string;
  production_status?: string;
  priority?: string;
  customer_code?: string;
  delivery_deadline?: string;
}

export interface OrderSummary {
  total_orders: number;
  active_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  completion_rate: number;
  pending_production: number;
  in_production: number;
  production_completed: number;
  total_value: number;
}

export interface CustomerOption {
  id: number;
  customer_code: string;
  company_name: string;
  contact_person: string;
  display_name: string;
}

class OrderManagementAPI {
  private baseURL = '/api/order-management';

  async getOrders(params: OrderManagementQueryParams = {}): Promise<{ data: PaginatedOrderManagementResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getOrder(id: number): Promise<{ data: OrderManagement }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createOrder(order: OrderManagementCreate): Promise<{ data: OrderManagement }> {
    const response = await api.post(this.baseURL, order);
    return response;
  }

  async updateOrder(id: number, order: OrderManagementUpdate): Promise<{ data: OrderManagement }> {
    const response = await api.put(`${this.baseURL}/${id}`, order);
    return response;
  }

  async deleteOrder(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getCustomersList(): Promise<{ data: CustomerOption[] }> {
    const response = await api.get(`${this.baseURL}/customers/list`);
    return response;
  }

  async getCategoriesList(): Promise<{ data: string[] }> {
    const response = await api.get(`${this.baseURL}/categories/list`);
    return response;
  }

  async deliverOrder(id: number, deliveryQuantity: number, deliveryNote?: string): Promise<{ data: OrderManagement }> {
    const response = await api.post(`${this.baseURL}/${id}/deliver`, {
      delivery_quantity: deliveryQuantity,
      delivery_note: deliveryNote
    });
    return response;
  }

  async getOrderSummary(): Promise<{ data: OrderSummary }> {
    const response = await api.get(`${this.baseURL}/dashboard/summary`);
    return response;
  }
}

export const orderManagementAPI = new OrderManagementAPI();
