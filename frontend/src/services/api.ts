import axios from 'axios';

// API 基礎配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';

// 建立 axios 實例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器 - 添加認證 token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 回應攔截器 - 處理錯誤
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token 過期或無效，重定向到登入頁面
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 認證 API
export const authAPI = {
  login: (credentials: { username: string; password: string }) =>
    api.post('/api/auth/login', credentials),
  
  logout: () =>
    api.post('/api/auth/logout'),
  
  getCurrentUser: () =>
    api.get('/api/auth/me'),
  
  getPermissions: () =>
    api.get('/api/auth/permissions'),
};

// 客戶訂貨 API
export const customerOrdersAPI = {
  getOrders: (params?: any) =>
    api.get('/api/customer-orders', { params }),
  
  getOrder: (id: number) =>
    api.get(`/api/customer-orders/${id}`),
  
  createOrder: (data: any) =>
    api.post('/api/customer-orders', data),
  
  updateOrder: (id: number, data: any) =>
    api.put(`/api/customer-orders/${id}`, data),
  
  deleteOrder: (id: number) =>
    api.delete(`/api/customer-orders/${id}`),
  
  getOrderProgress: (id: number) =>
    api.get(`/api/customer-orders/${id}/progress`),
  
  updateOrderStatus: (id: number, status: string) =>
    api.put(`/api/customer-orders/${id}/status`, { status }),
};

// 生產排程 API
export const productionScheduleAPI = {
  getWorkstations: () =>
    api.get('/api/production-schedule/workstations'),
  
  getDailySchedule: (date?: string) =>
    api.get('/api/production-schedule/schedule', { params: { schedule_date: date } }),
  
  getTasks: (params?: any) =>
    api.get('/api/production-schedule/tasks', { params }),
  
  createTask: (data: any) =>
    api.post('/api/production-schedule/tasks', data),
  
  updateTaskStatus: (taskId: number, status: string) =>
    api.put(`/api/production-schedule/tasks/${taskId}/status`, { status }),
  
  getWorkstationTasks: (workstation: string) =>
    api.get(`/api/production-schedule/workstations/${workstation}/tasks`),
  
  getUtilizationAnalytics: (startDate: string, endDate: string) =>
    api.get('/api/production-schedule/analytics/utilization', {
      params: { start_date: startDate, end_date: endDate }
    }),
};

// 數據分析 API
export const analyticsAPI = {
  getCustomerStats: (startDate: string, endDate: string) =>
    api.get('/api/analytics/customer-stats', {
      params: { start_date: startDate, end_date: endDate }
    }),

  getSalesTrend: (startDate: string, endDate: string) =>
    api.get('/api/analytics/sales-trend', {
      params: { start_date: startDate, end_date: endDate }
    }),

  getCustomerRanking: (startDate: string, endDate: string, limit?: number) =>
    api.get('/api/analytics/customer-ranking', {
      params: { start_date: startDate, end_date: endDate, limit }
    }),

  getProductSales: (startDate: string, endDate: string, limit?: number) =>
    api.get('/api/analytics/product-sales', {
      params: { start_date: startDate, end_date: endDate, limit }
    }),

  getProductionAnalytics: (startDate: string, endDate: string) =>
    api.get('/api/analytics/production-analytics', {
      params: { start_date: startDate, end_date: endDate }
    }),
};

// 儀表板 API
export const dashboardAPI = {
  getStats: () =>
    api.get('/api/dashboard/stats'),

  getWorkstationStatus: () =>
    api.get('/api/dashboard/workstation-status'),

  getRecentOrders: (limit?: number) =>
    api.get('/api/dashboard/recent-orders', {
      params: { limit }
    }),

  getSystemAlerts: () =>
    api.get('/api/dashboard/system-alerts'),

  getOverview: () =>
    api.get('/api/dashboard/overview'),
};

// 原物料訂購 API
export const materialOrdersAPI = {
  getOrders: (params?: any) =>
    api.get('/api/material-orders', { params }),
  
  getOrder: (id: number) =>
    api.get(`/api/material-orders/${id}`),
  
  createOrder: (data: any) =>
    api.post('/api/material-orders', data),
  
  updateOrderStatus: (id: number, status: string) =>
    api.put(`/api/material-orders/${id}/status`, { status }),
  
  getSuppliers: () =>
    api.get('/api/material-orders/suppliers'),
};

// 倉儲進銷存 API
export const inventoryAPI = {
  getItems: (params?: any) =>
    api.get('/api/inventory/items', { params }),
  
  getItem: (id: number) =>
    api.get(`/api/inventory/items/${id}`),
  
  createItem: (data: any) =>
    api.post('/api/inventory/items', data),
  
  updateItem: (id: number, data: any) =>
    api.put(`/api/inventory/items/${id}`, data),
  
  deleteItem: (id: number) =>
    api.delete(`/api/inventory/items/${id}`),

  getTransactions: (params?: any) =>
    api.get('/api/inventory/transactions', { params }),
  
  createTransaction: (data: any) =>
    api.post('/api/inventory/transactions', data),
  
  getStockLevelReport: () =>
    api.get('/api/inventory/reports/stock-level'),
  
  getLowStockAlerts: () =>
    api.get('/api/inventory/alerts/low-stock'),
    
  getCategories: () =>
    api.get('/api/inventory/categories'),
    
  getSuppliers: () =>
    api.get('/api/inventory/suppliers'),
    
  getLocations: () =>
    api.get('/api/inventory/locations'),
};

// 財務 API
export const financeAPI = {
  getFinancialReport: (period: string, startDate: string, endDate: string) =>
    api.get(`/api/finance/reports/${period}`, {
      params: { start_date: startDate, end_date: endDate }
    }),
  
  getAccountsReceivable: (params?: any) =>
    api.get('/api/finance/receivables', { params }),
  
  getAccountsPayable: (params?: any) =>
    api.get('/api/finance/payables', { params }),
  
  getCashFlowReport: (startDate: string, endDate: string) =>
    api.get('/api/finance/cash-flow', {
      params: { start_date: startDate, end_date: endDate }
    }),
};

// 人事薪資 API
export const hrPayrollAPI = {
  getEmployees: (params?: any) =>
    api.get('/api/hr-payroll/employees', { params }),
  
  createEmployee: (data: any) =>
    api.post('/api/hr-payroll/employees', data),
  
  getAttendanceRecords: (startDate: string, endDate: string, employeeId?: number) =>
    api.get('/api/hr-payroll/attendance', {
      params: { start_date: startDate, end_date: endDate, employee_id: employeeId }
    }),
  
  checkIn: (employeeId: number, checkInTime?: string) =>
    api.post('/api/hr-payroll/attendance/check-in', {
      employee_id: employeeId,
      check_in_time: checkInTime
    }),
  
  checkOut: (employeeId: number, checkOutTime?: string) =>
    api.post('/api/hr-payroll/attendance/check-out', {
      employee_id: employeeId,
      check_out_time: checkOutTime
    }),
  
  getPayrollRecords: (startDate: string, endDate: string, employeeId?: number) =>
    api.get('/api/hr-payroll/payroll', {
      params: { pay_period_start: startDate, pay_period_end: endDate, employee_id: employeeId }
    }),
  
  calculatePayroll: (startDate: string, endDate: string, employeeIds?: number[]) =>
    api.post('/api/hr-payroll/payroll/calculate', {
      pay_period_start: startDate,
      pay_period_end: endDate,
      employee_ids: employeeIds
    }),
  
  getAttendanceSummary: (startDate: string, endDate: string, department?: string) =>
    api.get('/api/hr-payroll/reports/attendance-summary', {
      params: { start_date: startDate, end_date: endDate, department }
    }),
};

// 商品資料 API
export const productDataAPI = {
  getProducts: (params?: any) =>
    api.get('/api/product-data', { params }),
  
  getProduct: (id: number) =>
    api.get(`/api/product-data/${id}`),
  
  createProduct: (data: any) =>
    api.post('/api/product-data', data),
  
  updateProduct: (id: number, data: any) =>
    api.put(`/api/product-data/${id}`, data),
  
  getCostAnalysis: (id: number) =>
    api.get(`/api/product-data/${id}/cost-analysis`),
  
  getBOM: (id: number) =>
    api.get(`/api/product-data/${id}/bom`),
  
  updateBOM: (id: number, bomItems: any[]) =>
    api.put(`/api/product-data/${id}/bom`, bomItems),
  
  getCategories: () =>
    api.get('/api/product-data/categories'),
  
  getProfitabilityReport: () =>
    api.get('/api/product-data/reports/profitability'),
};

// 客戶管理 API
export const customerManagementAPI = {
  getCustomers: (params?: any) =>
    api.get('/api/customer-management/', { params }),

  getCustomer: (id: number) =>
    api.get(`/api/customer-management/${id}/`),

  createCustomer: (data: any) =>
    api.post('/api/customer-management/', data),

  updateCustomer: (id: number, data: any) =>
    api.put(`/api/customer-management/${id}/`, data),

  deleteCustomer: (id: number) =>
    api.delete(`/api/customer-management/${id}/`),

  getSalesAnalysis: (id: number) =>
    api.get(`/api/customer-management/${id}/sales-analysis/`),
  
  getProductAnalysis: (id: number) =>
    api.get(`/api/customer-management/${id}/product-analysis`),
  
  getCustomerOrders: (id: number, params?: any) =>
    api.get(`/api/customer-management/${id}/orders`, { params }),
  
  getSalesRanking: (startDate: string, endDate: string, limit?: number) =>
    api.get('/api/customer-management/reports/sales-ranking', {
      params: { start_date: startDate, end_date: endDate, limit }
    }),
  
  getCreditStatus: () =>
    api.get('/api/customer-management/reports/credit-status'),
};

export default api;
