import api from './api';

export interface Supplier {
  id: number;
  supplier_code: string;
  company_name: string;
  company_name_vn?: string;
  supplier_type?: string;
  address?: string;
  tax_id?: string;
  invoice_required: boolean;
  phone?: string;
  bank_info?: string;
  payment_terms: number;
  contract_info?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SupplierCreate {
  supplier_code: string;
  company_name: string;
  company_name_vn?: string;
  supplier_type?: string;
  address?: string;
  tax_id?: string;
  invoice_required?: boolean;
  phone?: string;
  bank_info?: string;
  payment_terms?: number;
  contract_info?: string;
  notes?: string;
}

export interface SupplierUpdate {
  supplier_code?: string;
  company_name?: string;
  company_name_vn?: string;
  supplier_type?: string;
  address?: string;
  tax_id?: string;
  invoice_required?: boolean;
  phone?: string;
  bank_info?: string;
  payment_terms?: number;
  contract_info?: string;
  notes?: string;
  is_active?: boolean;
}

export interface PaginatedSupplierResponse {
  data: Supplier[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface SupplierQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  supplier_type?: string;
  is_active?: boolean;
}

class SupplierAPI {
  private baseURL = '/api/supplier-management';

  async getSuppliers(params: SupplierQueryParams = {}): Promise<{ data: PaginatedSupplierResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getSupplier(id: number): Promise<{ data: Supplier }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createSupplier(supplier: SupplierCreate): Promise<{ data: Supplier }> {
    const response = await api.post(this.baseURL, supplier);
    return response;
  }

  async updateSupplier(id: number, supplier: SupplierUpdate): Promise<{ data: Supplier }> {
    const response = await api.put(`${this.baseURL}/${id}`, supplier);
    return response;
  }

  async deleteSupplier(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getSupplierTypes(): Promise<{ data: string[] }> {
    const response = await api.get(`${this.baseURL}/types/list`);
    return response;
  }
}

export const supplierAPI = new SupplierAPI();
