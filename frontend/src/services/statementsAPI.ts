import api from './api';

export interface StatementItem {
  id?: number;
  sequence_no: number;
  delivery_date?: string;
  delivery_note_no?: string;
  product_name: string;
  product_name_vn?: string;
  specifications?: string;
  unit: string;
  quantity: number;
  unit_price: number;
  amount: number;
  notes?: string;
}

export interface Statement {
  id: number;
  statement_no: string;
  customer_id: number;
  customer_code: string;
  period_start: string;
  period_end: string;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  status: string;
  customer_confirmed: boolean;
  created_at: string;
  updated_at: string;
  items: StatementItem[];
  customer_name?: string;
  customer_address?: string;
  customer_tax_id?: string;
  customer_phone?: string;
}

export interface StatementCreate {
  customer_id: number;
  customer_code: string;
  period_start: string;
  period_end: string;
  subtotal?: number;
  tax_rate?: number;
  tax_amount?: number;
  total_amount?: number;
  status?: string;
  customer_confirmed?: boolean;
  items: StatementItem[];
}

export interface StatementUpdate {
  customer_id?: number;
  customer_code?: string;
  period_start?: string;
  period_end?: string;
  subtotal?: number;
  tax_rate?: number;
  tax_amount?: number;
  total_amount?: number;
  status?: string;
  customer_confirmed?: boolean;
  items?: StatementItem[];
}

export interface PaginatedStatementResponse {
  data: Statement[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface StatementQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  customer_id?: number;
}

export interface CustomerForStatement {
  id: number;
  customer_code: string;
  company_name: string;
  tax_id?: string;
  address?: string;
  phone?: string;
}

class StatementsAPI {
  private baseURL = '/api/statements';

  async getStatements(params: StatementQueryParams = {}): Promise<{ data: PaginatedStatementResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getStatement(id: number): Promise<{ data: Statement }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createStatement(statement: StatementCreate): Promise<{ data: Statement }> {
    const response = await api.post(this.baseURL, statement);
    return response;
  }

  async updateStatement(id: number, statement: StatementUpdate): Promise<{ data: Statement }> {
    const response = await api.put(`${this.baseURL}/${id}`, statement);
    return response;
  }

  async deleteStatement(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getCustomersForStatement(): Promise<{ data: CustomerForStatement[] }> {
    const response = await api.get(`${this.baseURL}/customers/list`);
    return response;
  }
}

export const statementsAPI = new StatementsAPI();
