import { useState, useMemo } from 'react';
import { useLocalStorage, generateId, filterData, sortData } from './useLocalStorage';

interface UseDataManagerOptions<T> {
  storageKey: string;
  initialData: T[];
  searchFields: (keyof T)[];
}

export function useDataManager<T extends { id: string }>({
  storageKey,
  initialData,
  searchFields
}: UseDataManagerOptions<T>) {
  const [data, setData] = useLocalStorage<T[]>(storageKey, initialData);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof T | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filters, setFilters] = useState<Record<string, any>>({});

  // 過濾和排序後的數據
  const filteredData = useMemo(() => {
    let result = data;

    // 搜尋過濾
    if (searchTerm) {
      result = filterData(result, searchTerm, searchFields);
    }

    // 其他過濾條件
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        result = result.filter(item => {
          const itemValue = (item as any)[key];
          if (typeof value === 'boolean') {
            return itemValue === value;
          }
          return itemValue === value;
        });
      }
    });

    // 排序
    if (sortField) {
      result = sortData(result, sortField, sortOrder);
    }

    return result;
  }, [data, searchTerm, sortField, sortOrder, filters, searchFields]);

  // 新增項目
  const addItem = (item: Omit<T, 'id'>) => {
    const newItem = { ...item, id: generateId() } as T;
    setData(prev => [...prev, newItem]);
    return newItem;
  };

  // 更新項目
  const updateItem = (id: string, updates: Partial<T>) => {
    setData(prev =>
      prev.map(item =>
        item.id === id ? { ...item, ...updates } : item
      )
    );
  };

  // 刪除項目
  const deleteItem = (id: string) => {
    setData(prev => prev.filter(item => item.id !== id));
  };

  // 批量刪除
  const deleteItems = (ids: string[]) => {
    setData(prev => prev.filter(item => !ids.includes(item.id)));
  };

  // 取得單一項目
  const getItem = (id: string) => {
    return data.find(item => item.id === id);
  };

  // 重置數據
  const resetData = () => {
    setData(initialData);
  };

  // 設定過濾條件
  const setFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 清除過濾條件
  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  // 排序處理
  const handleSort = (field: keyof T) => {
    if (sortField === field) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  return {
    // 數據
    data,
    filteredData,
    
    // 搜尋和過濾
    searchTerm,
    setSearchTerm,
    filters,
    setFilter,
    clearFilters,
    
    // 排序
    sortField,
    sortOrder,
    handleSort,
    
    // CRUD 操作
    addItem,
    updateItem,
    deleteItem,
    deleteItems,
    getItem,
    resetData,
    
    // 統計
    totalCount: data.length,
    filteredCount: filteredData.length,
  };
}
