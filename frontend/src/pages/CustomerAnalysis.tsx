import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Space,
  Button,
  message,
  Empty,
  Spin
} from 'antd';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer
} from 'recharts';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { analyticsAPI } from '../services/api';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const CustomerAnalysis: React.FC = () => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [selectedCustomer, setSelectedCustomer] = useState('all');

  // Clear filters
  const clearFilters = () => {
    setSelectedCustomer('all');
    message.success(t('messages.filtersCleared'));
  };

  // Data state
  const [loading, setLoading] = useState(false);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [productSalesData, setProductSalesData] = useState<any[]>([]);
  const [customerRankingData, setCustomerRankingData] = useState<any[]>([]);
  const [statsData, setStatsData] = useState({
    totalSales: 0,
    totalOrders: 0,
    avgOrderValue: 0,
    activeCustomers: 0
  });

  // Fetch statistics data
  const fetchStatsData = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    setLoading(true);
    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await analyticsAPI.getCustomerStats(startDate, endDate);
      setStatsData({
        totalSales: response.data.total_sales,
        totalOrders: response.data.total_orders,
        avgOrderValue: response.data.avg_order_value,
        activeCustomers: response.data.active_customers
      });
      message.success(t('customerAnalysis.statsUpdated'));
    } catch (error) {
      console.error('Failed to fetch statistics data:', error);
      message.error(t('customerAnalysis.fetchStatsFailed'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch sales trend data
  const fetchSalesData = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await analyticsAPI.getSalesTrend(startDate, endDate);
      const formattedData = response.data.map((item: any) => ({
        date: dayjs(item.date).format('MM/DD'),
        sales: item.sales,
        orders: item.orders
      }));
      setSalesData(formattedData);
    } catch (error) {
      console.error('Failed to fetch sales trend data:', error);
      message.error(t('customerAnalysis.fetchSalesTrendFailed'));
    }
  };

  // Fetch product sales data
  const fetchProductSalesData = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await analyticsAPI.getProductSales(startDate, endDate, 10);
      const formattedData = response.data.map((item: any) => ({
        name: item.product_name,
        value: item.sales,
        quantity: item.quantity,
        percentage: item.percentage
      }));
      setProductSalesData(formattedData);
    } catch (error) {
      console.error('Failed to fetch product sales data:', error);
      message.error(t('customerAnalysis.fetchProductSalesFailed'));
    }
  };

  // Fetch customer ranking data
  const fetchCustomerRankingData = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await analyticsAPI.getCustomerRanking(startDate, endDate, 10);
      const formattedData = response.data.map((item: any, index: number) => ({
        key: index + 1,
        rank: index + 1,
        customerName: item.customer_name,
        totalSales: item.total_sales,
        orderCount: item.order_count,
        avgOrderValue: item.avg_order_value
      }));
      setCustomerRankingData(formattedData);
    } catch (error) {
      console.error('Failed to fetch customer ranking data:', error);
      message.error(t('customerAnalysis.fetchCustomerRankingFailed'));
    }
  };

  // Fetch all data
  const fetchAllData = async () => {
    await Promise.all([
      fetchStatsData(),
      fetchSalesData(),
      fetchProductSalesData(),
      fetchCustomerRankingData()
    ]);
  };

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  useEffect(() => {
    fetchAllData();
  }, [dateRange, selectedCustomer]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const customerRankingColumns = [
    {
      title: t('customerAnalysis.rank'),
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      render: (rank: number) => (
        <span style={{ 
          fontWeight: 'bold',
          color: rank <= 3 ? '#f5222d' : 'inherit'
        }}>
          #{rank}
        </span>
      )
    },
    {
      title: t('customerAnalysis.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: t('customerAnalysis.totalSales'),
      dataIndex: 'totalSales',
      key: 'totalSales',
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('customerAnalysis.orderCount'),
      dataIndex: 'orderCount',
      key: 'orderCount',
    },
    {
      title: t('customerAnalysis.avgOrderValue'),
      dataIndex: 'avgOrderValue',
      key: 'avgOrderValue',
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('customerAnalysis.lastOrderDate'),
      dataIndex: 'lastOrderDate',
      key: 'lastOrderDate',
    }
  ];

  return (
    <div>
      <Title level={2}>{t('customerAnalysis.title')}</Title>

      {/* Filter controls */}
      <Card style={{ marginBottom: 24 }}>
        <Space size="large">
          <div>
            <span style={{ marginRight: 8 }}>{t('customerAnalysis.dateRange')}：</span>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              format="YYYY-MM-DD"
            />
          </div>
          <div>
            <span style={{ marginRight: 8 }}>{t('customerAnalysis.customer')}：</span>
            <Select
              value={selectedCustomer}
              onChange={setSelectedCustomer}
              style={{ width: 200 }}
            >
              <Select.Option value="all">{t('customerAnalysis.allCustomers')}</Select.Option>
              <Select.Option value="customer1">順興科技股份有限公司</Select.Option>
              <Select.Option value="customer2">台灣製造有限公司</Select.Option>
              <Select.Option value="customer3">精密工業股份有限公司</Select.Option>
            </Select>
          </div>
          <Button type="primary" onClick={fetchAllData} loading={loading}>
            {t('common.refreshData')}
          </Button>
          <Button onClick={clearFilters}>{t('customerAnalysis.clearFilters')}</Button>
          <Button>{t('customerAnalysis.exportReport')}</Button>
        </Space>
      </Card>

      {/* Statistics cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Spin spinning={loading}>
              <Statistic
                title={t('customerAnalysis.totalSales')}
                value={statsData.totalSales}
                suffix="VND"
                formatter={(value) => `${Number(value).toLocaleString()}`}
                valueStyle={{ color: '#3f8600' }}
              />
            </Spin>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Spin spinning={loading}>
              <Statistic
                title={t('customerAnalysis.totalOrders')}
                value={statsData.totalOrders}
                suffix={t('customerAnalysis.orderUnit')}
                valueStyle={{ color: '#1890ff' }}
              />
            </Spin>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Spin spinning={loading}>
              <Statistic
                title={t('customerAnalysis.avgOrderValue')}
                value={statsData.avgOrderValue}
                suffix="VND"
                formatter={(value) => `${Number(value).toLocaleString()}`}
                valueStyle={{ color: '#722ed1' }}
              />
            </Spin>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Spin spinning={loading}>
              <Statistic
                title={t('customerAnalysis.activeCustomers')}
                value={statsData.activeCustomers}
                suffix={t('customerAnalysis.customerUnit')}
                valueStyle={{ color: '#f5222d' }}
              />
            </Spin>
          </Card>
        </Col>
      </Row>

      {/* Chart area */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title={t('customerAnalysis.salesTrend')} loading={loading}>
            {!loading && salesData.length === 0 ? (
              <Empty
                description={t('customerAnalysis.noSalesTrendData')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                style={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
              />
            ) : !loading ? (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#8884d8"
                    name={t('customerAnalysis.salesAmount')}
                  />
                  <Line
                    type="monotone"
                    dataKey="orders"
                    stroke="#82ca9d"
                    name={t('customerAnalysis.orderCount')}
                    yAxisId="right"
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : null}
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('customerAnalysis.productSalesDistribution')} loading={loading}>
            {!loading && productSalesData.length === 0 ? (
              <Empty
                description={t('customerAnalysis.noProductSalesData')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                style={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
              />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={productSalesData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {productSalesData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Card title={t('customerAnalysis.customerRanking')}>
            <Table
              columns={customerRankingColumns}
              dataSource={customerRankingData}
              pagination={false}
              size="middle"
              loading={loading}
              locale={{
                emptyText: <Empty description={t('customerAnalysis.noCustomerRankingData')} image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CustomerAnalysis;
