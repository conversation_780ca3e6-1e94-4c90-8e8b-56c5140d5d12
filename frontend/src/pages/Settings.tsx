import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Table,
  Space,
  Modal,
  message,
  Typography,
  Divider,
  Row,
  Col,
  Upload,
  Avatar
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  BellOutlined,
  GlobalOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

interface User {
  id: string;
  username: string;
  fullName: string;
  email: string;
  role: string;
  isActive: boolean;
  lastLogin: string;
}

const Settings: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  // Fetch user list
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('請先登入');
        return;
      }

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.map((user: any) => ({
          id: user.id.toString(),
          username: user.username,
          fullName: user.full_name,
          email: user.email,
          role: user.role,
          isActive: user.is_active,
          lastLogin: user.created_at || t('settings.neverLoggedIn')
        })));
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);

        if (response.status === 401) {
          message.error('認證失敗，請重新登入');
          localStorage.removeItem('token');
          window.location.href = '/login';
        } else if (response.status === 403) {
          message.error('權限不足，只有管理員可以查看用戶列表');
        } else {
          message.error(errorData.detail || t('settings.fetchUsersFailed'));
        }
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      message.error(t('messages.networkError'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch users on component mount
  useEffect(() => {
    // 檢查當前用戶角色
    const userInfo = localStorage.getItem('user');
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo);
        console.log('當前用戶信息:', user);
        if (user.role !== 'admin') {
          message.warning(`當前用戶角色為 ${user.role}，只有管理員可以查看用戶列表`);
          return;
        }
      } catch (error) {
        console.error('解析用戶信息失敗:', error);
      }
    }

    fetchUsers();
  }, []);

  const handleAddUser = () => {
    setEditingUser(null);
    form.resetFields();
    setUserModalVisible(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue(user);
    setUserModalVisible(true);
  };

  const handleDeleteUser = (userId: string) => {
    Modal.confirm({
      title: t('settings.confirmDelete'),
      content: t('settings.confirmDeleteUser'),
      async onOk() {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            message.success(t('settings.userDeleted'));
            fetchUsers(); // Refresh user list
          } else {
            const errorData = await response.json();
            message.error(errorData.detail || t('settings.deleteUserFailed'));
          }
        } catch (error) {
          console.error('Delete user error:', error);
          message.error(t('settings.deleteFailed'));
        }
      },
    });
  };

  const handleSaveUser = async () => {
    try {
      const values = await form.validateFields();
      const token = localStorage.getItem('token');

      if (editingUser) {
        // Edit user
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/${editingUser.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: values.username,
            full_name: values.fullName,
            email: values.email,
            role: values.role,
            is_active: values.isActive,
            ...(values.password && { password: values.password })
          })
        });

        if (response.ok) {
          message.success(t('settings.userUpdated'));
          fetchUsers(); // Refresh user list
        } else {
          const errorData = await response.json();
          message.error(errorData.detail || t('settings.updateUserFailed'));
        }
      } else {
        // Add user
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: values.username,
            full_name: values.fullName,
            email: values.email,
            role: values.role,
            is_active: values.isActive,
            password: values.password
          })
        });

        if (response.ok) {
          message.success(t('settings.userAdded'));
          fetchUsers(); // Refresh user list
        } else {
          const errorData = await response.json();
          message.error(errorData.detail || t('settings.addUserFailed'));
        }
      }

      setUserModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Operation failed:', error);
      message.error(t('settings.operationFailed'));
    }
  };

  const userColumns = [
    {
      title: t('settings.username'),
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: t('settings.fullName'),
      dataIndex: 'fullName',
      key: 'fullName',
    },
    {
      title: t('settings.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('settings.role'),
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        const roleMap = {
          admin: t('settings.admin'),
          manager: t('settings.manager'),
          employee: t('settings.employee'),
          viewer: t('settings.viewer')
        };
        return roleMap[role as keyof typeof roleMap] || role;
      },
    },
    {
      title: t('settings.status'),
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Switch checked={isActive} size="small" />
      ),
    },
    {
      title: t('settings.lastLogin'),
      dataIndex: 'lastLogin',
      key: 'lastLogin',
    },
    {
      title: t('settings.operations'),
      key: 'actions',
      render: (_, record: User) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteUser(record.id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>{t('settings.title')}</Title>

      <Tabs
        defaultActiveKey="profile"
        items={[
          {
            key: 'profile',
            label: <span><UserOutlined />個人資料</span>,
            children: (
              <Card>
                <Row gutter={24}>
                  <Col span={8}>
                    <div style={{ textAlign: 'center', marginBottom: 24 }}>
                      <Avatar size={120} icon={<UserOutlined />} />
                      <div style={{ marginTop: 16 }}>
                        <Upload>
                          <Button icon={<UploadOutlined />}>更換頭像</Button>
                        </Upload>
                      </div>
                    </div>
                  </Col>
                  <Col span={16}>
                    <Form layout="vertical">
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="用戶名">
                            <Input defaultValue="admin" disabled />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="姓名">
                            <Input defaultValue="系統管理員" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="郵箱">
                            <Input defaultValue="<EMAIL>" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="電話">
                            <Input defaultValue="+886-2-1234-5678" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Form.Item label="部門">
                        <Input defaultValue="資訊部" />
                      </Form.Item>
                      <Form.Item>
                        <Button type="primary">保存變更</Button>
                      </Form.Item>
                    </Form>
                  </Col>
                </Row>
              </Card>
            )
          },

          {
            key: 'users',
            label: <span><UserOutlined />用戶管理</span>,
            children: (
              <Card
                title="用戶管理"
                extra={
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
                    新增用戶
                  </Button>
                }
              >
                <Table
                  columns={userColumns}
                  dataSource={users}
                  rowKey="id"
                  loading={loading}
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            )
          },

          {
            key: 'system',
            label: <span><SettingOutlined />系統設定</span>,
            children: (
              <Card title="基本設定">
                <Form layout="vertical">
                  <Form.Item label="公司名稱">
                    <Input defaultValue="順興公司" />
                  </Form.Item>
                  <Form.Item label="系統名稱">
                    <Input defaultValue="順興 ERP 系統" />
                  </Form.Item>
                  <Form.Item label="時區">
                    <Select defaultValue="Asia/Taipei">
                      <Select.Option value="Asia/Taipei">台北時間 (UTC+8)</Select.Option>
                      <Select.Option value="Asia/Ho_Chi_Minh">胡志明市時間 (UTC+7)</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="預設語言">
                    <Select defaultValue={i18n.language} onChange={(value) => i18n.changeLanguage(value)}>
                      <Select.Option value="zh-TW">繁體中文</Select.Option>
                      <Select.Option value="vi-VN">Tiếng Việt</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary">保存設定</Button>
                  </Form.Item>
                </Form>
              </Card>
            )
          },

          {
            key: 'security',
            label: <span><SecurityScanOutlined />安全設定</span>,
            children: (
              <>
                <Card title="密碼設定">
                  <Form layout="vertical">
                    <Form.Item label="當前密碼">
                      <Input.Password />
                    </Form.Item>
                    <Form.Item label="新密碼">
                      <Input.Password />
                    </Form.Item>
                    <Form.Item label="確認新密碼">
                      <Input.Password />
                    </Form.Item>
                    <Form.Item>
                      <Button type="primary">更新密碼</Button>
                    </Form.Item>
                  </Form>
                </Card>

                <Card title="安全選項" style={{ marginTop: 16 }}>
                  <Form layout="vertical">
                    <Form.Item>
                      <Space direction="vertical">
                        <div>
                          <Switch defaultChecked /> 啟用雙因素認證
                        </div>
                        <div>
                          <Switch defaultChecked /> 登入通知
                        </div>
                        <div>
                          <Switch /> 自動登出 (30分鐘無活動)
                        </div>
                      </Space>
                    </Form.Item>
                  </Form>
                </Card>
              </>
            )
          },

          {
            key: 'notifications',
            label: <span><BellOutlined />通知設定</span>,
            children: (
              <Card title="通知偏好">
                <Form layout="vertical">
                  <Form.Item>
                    <Space direction="vertical">
                      <div>
                        <Switch defaultChecked /> 庫存不足警告
                      </div>
                      <div>
                        <Switch defaultChecked /> 訂單狀態更新
                      </div>
                      <div>
                        <Switch /> 生產排程變更
                      </div>
                      <div>
                        <Switch defaultChecked /> 系統維護通知
                      </div>
                      <div>
                        <Switch /> 郵件通知
                      </div>
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            )
          }
        ]}
      />

      {/* 用戶編輯模態框 */}
      <Modal
        title={editingUser ? t('settings.editUser') : t('settings.addUser')}
        open={userModalVisible}
        onOk={handleSaveUser}
        onCancel={() => {
          setUserModalVisible(false);
          form.resetFields();
        }}
        okText={t('settings.save')}
        cancelText={t('settings.cancel')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="username"
            label={t('settings.username')}
            rules={[{ required: true, message: t('settings.usernameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="fullName"
            label={t('settings.fullName')}
            rules={[{ required: true, message: t('settings.fullNameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label={t('settings.email')}
            rules={[
              { required: true, message: t('settings.emailRequired') },
              { type: 'email', message: t('settings.emailInvalid') }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label={t('settings.role')}
            rules={[{ required: true, message: t('settings.roleRequired') }]}
          >
            <Select>
              <Select.Option value="admin">{t('settings.admin')}</Select.Option>
              <Select.Option value="manager">{t('settings.manager')}</Select.Option>
              <Select.Option value="employee">{t('settings.employee')}</Select.Option>
              <Select.Option value="viewer">{t('settings.viewer')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="isActive"
            label={t('settings.activeStatus')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          {!editingUser && (
            <Form.Item
              name="password"
              label={t('settings.password')}
              rules={[{ required: true, message: t('settings.passwordRequired') }]}
            >
              <Input.Password />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default Settings;
