import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  message,
  Tag,
  Upload,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 對應 Excel 商品資料的介面定義
interface ProductQuotation {
  id: string;
  quoteDate: string;          // 報價時間
  supplier: string;           // 供應商
  customerName: string;       // 客戶
  productCode: string;        // 編號/料號
  moldNumber: string;         // 刀模號
  category: string;           // 分類
  finishedSize: string;       // 成品規格
  materialWeight: string;     // 材質克重
  materialCode: string;       // 材質代號
  paperSize: string;          // 紙板尺寸
  doorWidth: number;          // 門幅
  cutWidth: number;           // 切寬
  cutLength: number;          // 切長
  smallSheets: number;        // 小張數
  largeSheets: number;        // 大張數
  cuttingCount: number;       // 開張數
  corrugatedType: string;     // 瓦楞
  pressingLine: string;       // 壓線
  meters: number;             // 米數
  areaSqm: number;            // 平方數
  paperUnitPrice: number;     // 紙板單價
  subtotalBeforeTax: number;  // 小計(未稅)
  moq: number;                // MOQ
  laborCost: number;          // 人工成本
  transportFee: number;       // 運輸費
  moldFee: number;            // 刀模費
  additionalCost: number;     // 額外開支
  totalCost: number;          // 總成本
  unitPrice: number;          // 單價
  profitAmount: number;       // 利潤
  profitMargin: number;       // 利潤率
}

const ProductQuotation: React.FC = () => {
  const { t } = useTranslation();
  const [quotations, setQuotations] = useState<ProductQuotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<any[]>([]);

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
  });

  // 定義表格欄位 - 對應 Excel 商品資料欄位
  const columns: ColumnsType<ProductQuotation> = [
    {
      title: t('productQuotation.quotationTime'),
      dataIndex: 'quoteDate',
      key: 'quoteDate',
      width: 100,
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
      sorter: true,
    },
    {
      title: t('productQuotation.customer'),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 100,
      fixed: 'left',
    },
    {
      title: t('productQuotation.productCode'),
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
      fixed: 'left',
    },
    {
      title: t('productQuotation.category'),
      dataIndex: 'category',
      key: 'category',
      width: 80,
      filters: [
        { text: t('common.outerBox'), value: '外箱' },
        { text: t('common.innerBox'), value: '內盒' },
        { text: t('common.cushion'), value: '墊片' },
        { text: t('common.paperBox'), value: '紙箱' },
      ],
    },
    {
      title: t('productQuotation.specifications'),
      dataIndex: 'finishedSize',
      key: 'finishedSize',
      width: 150,
      ellipsis: true,
    },
    {
      title: t('productQuotation.materialWeight'),
      dataIndex: 'materialWeight',
      key: 'materialWeight',
      width: 100,
    },
    {
      title: t('productQuotation.cutWidth'),
      dataIndex: 'cutWidth',
      key: 'cutWidth',
      width: 80,
      render: (value: number) => value || '-',
    },
    {
      title: t('productQuotation.cutLength'),
      dataIndex: 'cutLength',
      key: 'cutLength',
      width: 80,
      render: (value: number) => value || '-',
    },
    {
      title: t('productQuotation.moq'),
      dataIndex: 'moq',
      key: 'moq',
      width: 80,
      render: (value: number) => value ? value.toLocaleString() : '-',
    },
    {
      title: t('productQuotation.area'),
      dataIndex: 'areaSqm',
      key: 'areaSqm',
      width: 100,
      render: (value: number) => value ? value.toFixed(3) : '-',
    },
    {
      title: t('productQuotation.paperPrice'),
      dataIndex: 'paperUnitPrice',
      key: 'paperUnitPrice',
      width: 130,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('productQuotation.laborCost'),
      dataIndex: 'laborCost',
      key: 'laborCost',
      width: 100,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('productQuotation.shippingCost'),
      dataIndex: 'transportFee',
      key: 'transportFee',
      width: 80,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('common.totalCost'),
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('common.unitPrice'),
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('common.profit'),
      dataIndex: 'profitAmount',
      key: 'profitAmount',
      width: 100,
      render: (value: number) => value ? `${value.toLocaleString()} VND` : '-',
    },
    {
      title: t('common.profitMargin'),
      dataIndex: 'profitMargin',
      key: 'profitMargin',
      width: 80,
      render: (value: number) => {
        const margin = value || 0;
        const color = margin >= 30 ? '#52c41a' : margin >= 15 ? '#1890ff' : margin >= 5 ? '#faad14' : '#ff4d4f';
        return <span style={{ color }}>{margin.toFixed(2)}%</span>;
      },
    },
    {
      title: t('productQuotation.operations'),
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record: ProductQuotation) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            {t('productQuotation.edit')}
          </Button>
          <Button
            type="link"
            size="small"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record.id)}
          >
            {t('productQuotation.delete')}
          </Button>
        </Space>
      ),
    },
  ];

  // 從 API 獲取資料
  const fetchQuotations = async (page = 1, pageSize = 20, filters = {}) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(categoryFilter && { category: categoryFilter }),
        ...(customerFilter && { customer: customerFilter }),
        ...filters,
      });

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/excel-data/product-quotations?${params}`);
      if (!response.ok) {
        throw new Error(t('messages.fetchDataFailed'));
      }

      const data = await response.json();
      setQuotations(data.data || []);
      setPagination(prev => ({
        ...prev,
        current: data.page,
        pageSize: data.pageSize,
        total: data.total,
      }));

    } catch (error) {
      console.error('Failed to fetch quotation data:', error);
      message.error(t('messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 組件掛載時獲取資料
  React.useEffect(() => {
    fetchQuotations();
  }, []);

  // 處理函數
  const handleEdit = (record: ProductQuotation) => {
    console.log('Edit quotation:', record);
  };

  const handleDelete = (id: string) => {
    console.log('Delete quotation:', id);
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchQuotations(1, pagination.pageSize);
  };

  const handleExcelImport = () => {
    console.log('Import Excel');
  };

  const handleExcelExport = () => {
    console.log('Export Excel');
  };

  // 處理分頁變更
  const handleTableChange = (page: number, pageSize?: number) => {
    fetchQuotations(page, pageSize || pagination.pageSize);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title={t('productQuotation.title')} style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Input
              placeholder={t('productQuotation.searchPlaceholder')}
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('common.selectCategory')}
              value={categoryFilter}
              onChange={setCategoryFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="外箱">{t('common.outerBox')}</Option>
              <Option value="內盒">{t('common.innerBox')}</Option>
              <Option value="墊片">{t('common.cushion')}</Option>
              <Option value="紙箱">{t('common.paperBox')}</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('common.selectCustomer')}
              value={customerFilter}
              onChange={setCustomerFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="BOLY">BOLY</Option>
              <Option value="IBO">IBO</Option>
              <Option value="LOA">LOA</Option>
              <Option value="DIEUTUONG">DIEUTUONG</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={[t('productQuotation.fromDate'), t('productQuotation.toDate')]}
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                {t('common.search')}
              </Button>
              <Button
                icon={<PlusOutlined />}
                onClick={() => console.log('Add quotation')}
              >
                {t('productQuotation.addProduct')}
              </Button>
            </Space>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col span={24}>
            <Space>
              <Upload>
                <Button icon={<UploadOutlined />} onClick={handleExcelImport}>
                  {t('productQuotation.importExcel')}
                </Button>
              </Upload>
              <Button icon={<DownloadOutlined />} onClick={handleExcelExport}>
                {t('productQuotation.exportExcel')}
              </Button>
              <Button icon={<FileExcelOutlined />}>
                {t('productQuotation.downloadTemplate')}
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={quotations}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 2000, y: 600 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default ProductQuotation;
