import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  message,
  Modal
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { customerOrdersAPI } from '../services/api';

import DetailModal from '../components/DetailModal';
import FormModal from '../components/FormModal';
import SearchFilters from '../components/SearchFilters';

const { Title } = Typography;
const { confirm } = Modal;

interface CustomerOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  orderDate: string;
  deliveryDate: string;
  status: string;
  totalAmount: number;
  progress: number;
  items: Array<{
    productName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

const CustomerOrders: React.FC = () => {
  const { t } = useTranslation();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<CustomerOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<CustomerOrder | null>(null);

  // 狀態管理
  const [orders, setOrders] = useState<CustomerOrder[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<any>({});

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 從後端獲取訂單數據
  const fetchOrders = async (page = 1, pageSize = 20, searchFilters = {}) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...searchFilters,
        ...filters,
      };

      const response = await customerOrdersAPI.getOrders(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;
        // 轉換數據格式以匹配前端接口
        const formattedOrders = data.map((order: any) => ({
          id: order.id.toString(),
          orderNumber: order.order_no || `ORD-${order.id.toString().padStart(6, '0')}`,
          customerName: order.customer_name,
          orderDate: order.order_date ? order.order_date.split('T')[0] : '',
          deliveryDate: order.delivery_date ? order.delivery_date.split('T')[0] : '',
          status: order.status,
          totalAmount: order.total_amount,
          progress: order.status === 'completed' ? 100 : order.status === 'production' ? 65 : 0,
          items: order.items.map((item: any) => ({
            productName: item.product_name,
            quantity: item.quantity,
            unitPrice: item.unit_price,
            totalPrice: item.total_price
          })),
          notes: order.notes || '',
          createdAt: order.created_at,
          updatedAt: order.updated_at
        }));
        setOrders(formattedOrders);
        setPagination(prev => ({
          ...prev,
          current: currentPage,
          pageSize: currentPageSize,
          total: total,
        }));
        message.success(`成功載入 ${formattedOrders.length} 筆訂單資料，共 ${total} 筆`);
    } catch (error) {
      console.error('獲取訂單數據錯誤:', error);
      message.error('網路連線錯誤');
    } finally {
      setLoading(false);
    }
  };

  // 處理分頁變更
  const handleTableChange = (page: number, pageSize?: number) => {
    fetchOrders(page, pageSize || pagination.pageSize);
  };

  // 處理搜尋
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchOrders(1, pagination.pageSize);
  };

  // 獲取客戶列表
  const fetchCustomers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/customer-management/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCustomers(data.data || data);
      } else {
        console.error('獲取客戶列表失敗');
      }
    } catch (error) {
      console.error('獲取客戶列表錯誤:', error);
    }
  };

  // 組件掛載時獲取數據
  useEffect(() => {
    fetchOrders();
    fetchCustomers();
  }, []);

  // 篩選訂單
  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchTerm ||
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status || order.status === filters.status;

    return matchesSearch && matchesStatus;
  });

  const setFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'confirmed': return 'blue';
      case 'production': return 'purple';
      case 'completed': return 'green';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t('customerOrders.pending');
      case 'confirmed': return t('customerOrders.confirmed');
      case 'production': return t('customerOrders.production');
      case 'completed': return t('customerOrders.completed');
      case 'cancelled': return t('customerOrders.cancelled');
      default: return status;
    }
  };

  // 處理查看詳情
  const handleView = (record: CustomerOrder) => {
    setSelectedOrder(record);
    setDetailVisible(true);
  };

  // 處理編輯
  const handleEdit = (record: CustomerOrder) => {
    setEditingOrder(record);
    setFormVisible(true);
  };

  // 處理刪除
  const handleDelete = async (record: CustomerOrder) => {
    confirm({
      title: '確認刪除',
      icon: <ExclamationCircleOutlined />,
      content: `確定要刪除訂單 ${record.orderNumber} 嗎？`,
      async onOk() {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/customer-orders/${record.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            message.success('訂單已刪除');
            fetchOrders(); // 重新獲取訂單列表
          } else {
            const errorData = await response.json();
            message.error(errorData.detail || '刪除訂單失敗');
          }
        } catch (error) {
          console.error('刪除訂單錯誤:', error);
          message.error('刪除失敗，請稍後再試');
        }
      },
    });
  };

  // 處理新增
  const handleAdd = () => {
    setEditingOrder(null);
    setFormVisible(true);
  };

  // 處理表單提交
  const handleFormSubmit = async (values: any) => {
    try {
      const token = localStorage.getItem('token');
      // 處理日期格式
      const formatDate = (date: any) => {
        if (!date) return new Date().toISOString().split('T')[0];
        if (typeof date === 'string') return date;
        if (date.format) return date.format('YYYY-MM-DD');
        return new Date(date).toISOString().split('T')[0];
      };

      // 驗證必填字段
      if (!values.customerId) {
        message.error('請選擇客戶');
        return;
      }

      // 找到選中的客戶
      const selectedCustomer = customers.find(c => c.id === values.customerId);

      const orderData = {
        customer_id: values.customerId,
        customer_name: selectedCustomer ? selectedCustomer.company_name : '未知客戶',
        order_date: formatDate(values.orderDate),
        delivery_date: formatDate(values.deliveryDate),
        notes: values.notes || '',
        items: values.items || [
          {
            product_id: 1,
            product_name: '包裝盒A',
            quantity: values.quantity || 100,
            unit_price: values.unitPrice || 15.0,
            total_price: (values.quantity || 100) * (values.unitPrice || 15.0),
            specifications: values.specifications || ''
          }
        ]
      };

      if (editingOrder) {
        // 更新訂單
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/customer-orders/${editingOrder.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(orderData)
        });

        if (response.ok) {
          message.success('訂單已更新');
          fetchOrders(); // 重新獲取訂單列表
        } else {
          const errorData = await response.json();
          message.error(errorData.detail || '更新訂單失敗');
        }
      } else {
        // 創建新訂單
        console.log('發送訂單數據:', orderData);
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/customer-orders/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(orderData)
        });

        if (response.ok) {
          message.success('訂單已新增');
          fetchOrders(); // 重新獲取訂單列表
        } else {
          const errorData = await response.json();
          console.error('新增訂單失敗，錯誤詳情:', errorData);
          message.error(errorData.detail || '新增訂單失敗');
        }
      }

      setFormVisible(false);
      setEditingOrder(null);
    } catch (error) {
      console.error('提交訂單錯誤:', error);
      message.error('操作失敗，請稍後再試');
    }
  };

  // 批量刪除
  const handleBatchDelete = () => {
    console.log('批量刪除被調用，選中的行:', selectedRowKeys);

    if (selectedRowKeys.length === 0) {
      message.warning('請選擇要刪除的訂單');
      return;
    }

    // 使用 window.confirm 作為備選方案
    const confirmed = window.confirm(`確定要刪除選中的 ${selectedRowKeys.length} 個訂單嗎？`);

    if (!confirmed) {
      console.log('用戶取消了批量刪除');
      return;
    }

    console.log('用戶確認刪除，開始執行批量刪除...');

    // 執行批量刪除的邏輯
    const executeBatchDelete = async () => {
      try {
        const token = localStorage.getItem('token');
        console.log('使用的 token:', token ? '已設置' : '未設置');

        // 逐個刪除並檢查結果
        const deleteResults = [];
        for (const id of selectedRowKeys) {
          console.log(`正在刪除訂單 ID: ${id}`);

          const url = `${import.meta.env.VITE_API_BASE_URL}/api/customer-orders/${id}`;
          console.log('請求 URL:', url);

          const response = await fetch(url, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          console.log(`刪除訂單 ${id} 的響應狀態:`, response.status);
          deleteResults.push({ id, success: response.ok, status: response.status });
        }

        console.log('批量刪除結果:', deleteResults);

        const successCount = deleteResults.filter(r => r.success).length;
        const failCount = deleteResults.length - successCount;

        if (successCount > 0) {
          setSelectedRowKeys([]);
          fetchOrders(); // 重新獲取訂單列表

          if (failCount === 0) {
            message.success(`成功刪除 ${successCount} 個訂單`);
          } else {
            message.warning(`成功刪除 ${successCount} 個訂單，${failCount} 個失敗`);
          }
        } else {
          message.error('批量刪除失敗');
        }
      } catch (error) {
        console.error('批量刪除錯誤:', error);
        message.error('批量刪除失敗，請稍後再試');
      }
    };

    executeBatchDelete();
  };

  const columns: ColumnsType<CustomerOrder> = [
    {
      title: t('customerOrders.orderNumber'),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
    },
    {
      title: t('customerOrders.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: t('customerOrders.orderDate'),
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
    },
    {
      title: t('customerOrders.deliveryDate'),
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 120,
    },
    {
      title: t('customerOrders.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: t('customerOrders.totalAmount'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => `NT$ ${(amount || 0).toLocaleString()}`,
    },
    {
      title: t('customerOrders.progress'),
      dataIndex: 'progress',
      key: 'progress',
      width: 100,
      render: (progress: number) => `${progress}%`,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            title="查看詳情"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="編輯"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            title="刪除"
          />
        </Space>
      ),
    },
  ];

  // 搜尋篩選欄位配置
  const filterFields = [
    {
      key: 'search',
      label: '搜尋',
      type: 'search' as const,
      placeholder: `${t('common.search')}${t('customerOrders.orderNumber')}或客戶名稱`,
      span: 8
    },
    {
      key: 'status',
      label: '狀態',
      type: 'select' as const,
      options: [
        { label: t('customerOrders.pending'), value: 'pending' },
        { label: t('customerOrders.confirmed'), value: 'confirmed' },
        { label: t('customerOrders.production'), value: 'production' },
        { label: t('customerOrders.completed'), value: 'completed' },
        { label: t('customerOrders.cancelled'), value: 'cancelled' }
      ],
      span: 6
    },
    {
      key: 'dateRange',
      label: '日期範圍',
      type: 'dateRange' as const,
      span: 8
    }
  ];

  // 表單欄位配置
  const formFields = [
    {
      name: 'customerId',
      label: '客戶',
      type: 'select' as const,
      required: true,
      span: 12,
      options: customers.map(customer => ({
        label: customer.company_name,
        value: customer.id
      }))
    },
    { name: 'orderDate', label: '訂單日期', type: 'date' as const, required: true, span: 12 },
    { name: 'deliveryDate', label: '交貨日期', type: 'date' as const, required: true, span: 12 },
    { name: 'quantity', label: '數量', type: 'number' as const, required: true, span: 8 },
    { name: 'unitPrice', label: '單價', type: 'number' as const, required: true, span: 8 },
    { name: 'specifications', label: '規格', type: 'text' as const, span: 8 },
    { name: 'status', label: '狀態', type: 'select' as const, required: true, span: 12,
      options: [
        { label: t('customerOrders.pending'), value: 'pending' },
        { label: t('customerOrders.confirmed'), value: 'confirmed' },
        { label: t('customerOrders.production'), value: 'production' },
        { label: t('customerOrders.completed'), value: 'completed' }
      ]
    },
    { name: 'notes', label: '備註', type: 'textarea' as const, span: 24 }
  ];

  // 詳情欄位配置
  const detailFields = [
    { label: '訂單編號', key: 'orderNumber' },
    { label: '客戶名稱', key: 'customerName' },
    { label: '訂單日期', key: 'orderDate', type: 'date' as const },
    { label: '交貨日期', key: 'deliveryDate', type: 'date' as const },
    { label: '狀態', key: 'status', type: 'tag' as const },
    { label: '總金額', key: 'totalAmount', type: 'currency' as const },
    { label: '進度', key: 'progress', render: (value: number) => `${value}%` },
    { label: '備註', key: 'notes', span: 2 },
    { label: '建立時間', key: 'createdAt', type: 'date' as const },
    { label: '更新時間', key: 'updatedAt', type: 'date' as const }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      console.log('=== 行選擇變更 ===');
      console.log('舊的選中行:', selectedRowKeys);
      console.log('新的選中行:', newSelectedRowKeys);
      setSelectedRowKeys(newSelectedRowKeys);
    },
    onSelect: (record: CustomerOrder, selected: boolean, selectedRows: CustomerOrder[]) => {
      console.log('=== 單行選擇 ===');
      console.log('選中的記錄:', record);
      console.log('是否選中:', selected);
      console.log('所有選中的行:', selectedRows);
    },
    onSelectAll: (selected: boolean, selectedRows: CustomerOrder[], changeRows: CustomerOrder[]) => {
      console.log('=== 全選/取消全選 ===');
      console.log('是否全選:', selected);
      console.log('所有選中的行:', selectedRows);
      console.log('變更的行:', changeRows);
    },
  };

  return (
    <div>
      <Title level={2}>{t('customerOrders.title')}</Title>

      {/* 統計卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="總訂單數"
              value={orders.length}
              suffix="筆"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="進行中訂單"
              value={orders.filter(o => o.status === 'production').length}
              suffix="筆"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="總金額"
              value={orders.reduce((sum, o) => sum + o.totalAmount, 0)}
              prefix="NT$"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均訂單金額"
              value={orders.length > 0 ? Math.round(orders.reduce((sum, o) => sum + o.totalAmount, 0) / orders.length) : 0}
              prefix="NT$"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 搜尋和篩選區域 */}
        <SearchFilters
          fields={filterFields}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filters={filters}
          onFilterChange={setFilter}
          onClear={clearFilters}
          onAdd={handleAdd}
          addButtonText={t('common.add')}
        />

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Space>
              <span>已選擇 {selectedRowKeys.length} 項</span>
              <Button danger onClick={handleBatchDelete}>
                批量刪除
              </Button>
            </Space>
          </div>
        )}

        {/* 表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
        />
      </Card>

      {/* 詳情查看對話框 */}
      <DetailModal
        title={`${selectedOrder?.orderNumber} - 訂單詳情`}
        visible={detailVisible}
        onClose={() => setDetailVisible(false)}
        data={selectedOrder}
        fields={detailFields}
      />

      {/* 新增/編輯對話框 */}
      <FormModal
        title={editingOrder ? '編輯訂單' : '新增訂單'}
        visible={formVisible}
        onOk={handleFormSubmit}
        onCancel={() => {
          setFormVisible(false);
          setEditingOrder(null);
        }}
        fields={formFields}
        initialValues={editingOrder}
      />
    </div>
  );
};

export default CustomerOrders;
