import React from 'react';
import { Card, Typography, Space } from 'antd';
import { ToolOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface PlaceholderPageProps {
  title: string;
  description?: string;
}

const PlaceholderPage: React.FC<PlaceholderPageProps> = ({ title, description }) => {
  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          <ToolOutlined style={{ fontSize: '64px', color: '#1890ff' }} />
          <Title level={2}>{title}</Title>
          <Text type="secondary">
            {description || '此功能正在開發中，敬請期待...'}
          </Text>
        </Space>
      </Card>
    </div>
  );
};

export default PlaceholderPage;
