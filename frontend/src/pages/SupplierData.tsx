import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Switch,
  Typography,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { supplierAPI, type Supplier, type SupplierCreate, type SupplierUpdate } from '../services/supplierAPI';

const { Title } = Typography;
const { TextArea } = Input;

const SupplierData: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentSupplier, setCurrentSupplier] = useState<Supplier | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(true); // 預設只顯示活躍的供應商
  const [supplierTypes, setSupplierTypes] = useState<string[]>([]);

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取供應商列表
  const fetchSuppliers = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(typeFilter && { supplier_type: typeFilter }),
        ...(statusFilter !== undefined && { is_active: statusFilter }),
      };

      const response = await supplierAPI.getSuppliers(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setSuppliers(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch suppliers:', error);
      message.error('獲取供應商資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取供應商類型
  const fetchSupplierTypes = async () => {
    try {
      const response = await supplierAPI.getSupplierTypes();
      setSupplierTypes(response.data);
    } catch (error) {
      console.error('Failed to fetch supplier types:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchSuppliers();
    fetchSupplierTypes();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchSuppliers(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setTypeFilter('');
    setStatusFilter(true); // 重置時仍然只顯示活躍的供應商
    setTimeout(() => {
      fetchSuppliers(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchSuppliers(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentSupplier(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (supplier: Supplier) => {
    setEditMode(true);
    setCurrentSupplier(supplier);
    form.setFieldsValue({
      supplier_code: supplier.supplier_code,
      company_name: supplier.company_name,
      company_name_vn: supplier.company_name_vn,
      supplier_type: supplier.supplier_type,
      address: supplier.address,
      tax_id: supplier.tax_id,
      invoice_required: supplier.invoice_required,
      phone: supplier.phone,
      bank_info: supplier.bank_info,
      payment_terms: supplier.payment_terms,
      contract_info: supplier.contract_info,
      notes: supplier.notes,
      is_active: supplier.is_active,
    });
    setModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (editMode && currentSupplier) {
        // 更新供應商
        await supplierAPI.updateSupplier(currentSupplier.id, values as SupplierUpdate);
        message.success('供應商資料更新成功');
      } else {
        // 新增供應商
        await supplierAPI.createSupplier(values as SupplierCreate);
        message.success('供應商資料新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchSuppliers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('Failed to save supplier:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除供應商
  const handleDelete = async (supplier: Supplier) => {
    try {
      await supplierAPI.deleteSupplier(supplier.id);
      message.success('供應商已刪除');
      fetchSuppliers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Failed to delete supplier:', error);
      message.error('刪除失敗');
    }
  };

  // 表格欄位定義
  const columns = [
    {
      title: '供應商代碼',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '公司全稱',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 200,
    },
    {
      title: '種類',
      dataIndex: 'supplier_type',
      key: 'supplier_type',
      width: 100,
      render: (type: string) => type ? <Tag color="blue">{type}</Tag> : '-',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '稅號',
      dataIndex: 'tax_id',
      key: 'tax_id',
      width: 120,
    },
    {
      title: '開票',
      dataIndex: 'invoice_required',
      key: 'invoice_required',
      width: 80,
      render: (required: boolean) => (
        <Tag color={required ? 'green' : 'red'}>
          {required ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '電話',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '帳期(天)',
      dataIndex: 'payment_terms',
      key: 'payment_terms',
      width: 100,
      render: (days: number) => `${days}天`,
    },
    {
      title: '狀態',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '啟用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: Supplier) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除這個供應商嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>供應商資料管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Input
              placeholder="搜尋供應商代碼、公司名稱或電話"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="篩選類型"
              style={{ width: '100%' }}
              allowClear
              value={typeFilter || undefined}
              onChange={(value) => setTypeFilter(value || '')}
            >
              <Select.Option value="紙板供應商">紙板供應商</Select.Option>
              <Select.Option value="輔料供應商">輔料供應商</Select.Option>
              <Select.Option value="紙箱廠(同行)">紙箱廠(同行)</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter}
              onChange={(value) => setStatusFilter(value)}
            >
              <Select.Option value={true}>啟用</Select.Option>
              <Select.Option value={false}>停用</Select.Option>
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增供應商
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯供應商' : '新增供應商'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={800}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              payment_terms: 30,
              invoice_required: false,
              is_active: true,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="supplier_code"
                  label="供應商代碼"
                  rules={[{ required: true, message: '請輸入供應商代碼' }]}
                >
                  <Input placeholder="輸入供應商代碼" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="company_name"
                  label="公司全稱"
                  rules={[{ required: true, message: '請輸入公司全稱' }]}
                >
                  <Input placeholder="輸入公司全稱" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="company_name_vn"
                  label="越南文公司名稱"
                >
                  <Input placeholder="輸入越南文公司名稱" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="supplier_type"
                  label="種類"
                  rules={[{ required: true, message: '請選擇供應商類型' }]}
                >
                  <Select
                    placeholder="選擇供應商類型"
                    allowClear
                  >
                    <Select.Option value="紙板供應商">紙板供應商</Select.Option>
                    <Select.Option value="輔料供應商">輔料供應商</Select.Option>
                    <Select.Option value="紙箱廠(同行)">紙箱廠(同行)</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="address"
              label="地址"
            >
              <TextArea rows={2} placeholder="輸入地址" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="tax_id"
                  label="稅號/MST"
                >
                  <Input placeholder="輸入稅號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="phone"
                  label="電話/SĐT"
                >
                  <Input placeholder="輸入電話號碼" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="invoice_required"
                  label="是否開票/HD"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="是" unCheckedChildren="否" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="payment_terms"
                  label="帳期/NGÀY CÔNG NỢ (天)"
                  rules={[{ required: true, message: '請輸入帳期天數' }]}
                >
                  <Input type="number" placeholder="30" min={0} />
                </Form.Item>
              </Col>
              {editMode && (
                <Col span={8}>
                  <Form.Item
                    name="is_active"
                    label="狀態"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="啟用" unCheckedChildren="停用" />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Form.Item
              name="bank_info"
              label="銀行資訊/SỐ TÀI KHOẢN"
            >
              <TextArea rows={2} placeholder="輸入銀行帳戶資訊" />
            </Form.Item>

            <Form.Item
              name="contract_info"
              label="合約/HỢP ĐỒNG"
            >
              <TextArea rows={2} placeholder="輸入合約資訊" />
            </Form.Item>

            <Form.Item
              name="notes"
              label="备注/GHI CHÚ"
            >
              <TextArea rows={3} placeholder="輸入備註" />
            </Form.Item>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default SupplierData;
