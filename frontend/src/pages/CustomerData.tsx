import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Switch,
  Typography,
  Tag,
  InputNumber
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { customerManagementAPI } from '../services/api';

const { Title } = Typography;
const { TextArea } = Input;

interface Customer {
  id: number;
  customer_code: string;
  company_name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  tax_id?: string;
  invoice_required: boolean;
  company_shipping_fee: number;
  external_shipping_fee: number;
  payment_terms: number;
  contract_info?: string;
  contract_returned: boolean;
  notes?: string;
  credit_limit: number;
  status: string;
  created_at: string;
  updated_at: string;
  total_orders?: number;
  total_sales_amount?: number;
}

interface CustomerCreate {
  customer_code: string;
  company_name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  tax_id?: string;
  invoice_required?: boolean;
  company_shipping_fee?: number;
  external_shipping_fee?: number;
  payment_terms?: number;
  contract_info?: string;
  contract_returned?: boolean;
  notes?: string;
  credit_limit?: number;
}

const CustomerData: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取客戶列表
  const fetchCustomers = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      };

      const response = await customerManagementAPI.getCustomers(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setCustomers(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      message.error('獲取客戶資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchCustomers();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchCustomers(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setStatusFilter('');
    setTimeout(() => {
      fetchCustomers(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchCustomers(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentCustomer(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (customer: Customer) => {
    setEditMode(true);
    setCurrentCustomer(customer);
    form.setFieldsValue({
      customer_code: customer.customer_code,
      company_name: customer.company_name,
      contact_person: customer.contact_person,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
      tax_id: customer.tax_id,
      invoice_required: customer.invoice_required,
      company_shipping_fee: customer.company_shipping_fee,
      external_shipping_fee: customer.external_shipping_fee,
      payment_terms: customer.payment_terms,
      contract_info: customer.contract_info,
      contract_returned: customer.contract_returned,
      notes: customer.notes,
      credit_limit: customer.credit_limit,
    });
    setModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (editMode && currentCustomer) {
        // 更新客戶
        await customerManagementAPI.updateCustomer(currentCustomer.id, values as CustomerCreate);
        message.success('客戶資料更新成功');
      } else {
        // 新增客戶
        await customerManagementAPI.createCustomer(values as CustomerCreate);
        message.success('客戶資料新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchCustomers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('Failed to save customer:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除客戶
  const handleDelete = async (customer: Customer) => {
    try {
      await customerManagementAPI.deleteCustomer(customer.id);
      message.success('客戶已刪除');
      fetchCustomers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Failed to delete customer:', error);
      message.error('刪除失敗');
    }
  };

  // 表格欄位定義
  const columns = [
    {
      title: '公司簡稱',
      dataIndex: 'customer_code',
      key: 'customer_code',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '公司全稱',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 200,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '稅號',
      dataIndex: 'tax_id',
      key: 'tax_id',
      width: 120,
    },
    {
      title: '開票',
      dataIndex: 'invoice_required',
      key: 'invoice_required',
      width: 80,
      render: (required: boolean) => (
        <Tag color={required ? 'green' : 'red'}>
          {required ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '公司車運費',
      dataIndex: 'company_shipping_fee',
      key: 'company_shipping_fee',
      width: 120,
      render: (fee: number) => `₫${fee?.toLocaleString() || 0}`,
    },
    {
      title: '外車運費',
      dataIndex: 'external_shipping_fee',
      key: 'external_shipping_fee',
      width: 120,
      render: (fee: number) => `₫${fee?.toLocaleString() || 0}`,
    },
    {
      title: '電話',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '帳期(天)',
      dataIndex: 'payment_terms',
      key: 'payment_terms',
      width: 100,
      render: (days: number) => `${days}天`,
    },
    {
      title: '合約已取回',
      dataIndex: 'contract_returned',
      key: 'contract_returned',
      width: 120,
      render: (returned: boolean) => (
        <Tag color={returned ? 'green' : 'orange'}>
          {returned ? '已取回' : '未取回'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: Customer) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除這個客戶嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>客戶資料管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜尋公司簡稱、公司全稱或聯絡人"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="ACTIVE">啟用</Select.Option>
              <Select.Option value="INACTIVE">停用</Select.Option>
            </Select>
          </Col>
          <Col span={12}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增客戶
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={customers}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1600 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯客戶' : '新增客戶'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={900}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              payment_terms: 30,
              invoice_required: false,
              contract_returned: false,
              company_shipping_fee: 0,
              external_shipping_fee: 0,
              credit_limit: 0,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="customer_code"
                  label="公司簡稱"
                  rules={[{ required: true, message: '請輸入公司簡稱' }]}
                >
                  <Input placeholder="輸入公司簡稱" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="company_name"
                  label="公司全稱 / Tên đầy đủ"
                  rules={[{ required: true, message: '請輸入公司全稱' }]}
                >
                  <Input placeholder="輸入公司全稱" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="address"
              label="地址 / Địa chỉ"
            >
              <TextArea rows={2} placeholder="輸入地址" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="tax_id"
                  label="稅號 / MST"
                >
                  <Input placeholder="輸入稅號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="invoice_required"
                  label="是否開票 / HD"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="是" unCheckedChildren="否" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="company_shipping_fee"
                  label="運費 公司車 / PHÍ VẬN CHUYỂN Anh Ty"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="0"
                    min={0}
                    formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="external_shipping_fee"
                  label="運費 外車 / PHÍ VẬN CHUYỂN xe ngoài"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="0"
                    min={0}
                    formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="phone"
                  label="電話 / SĐT"
                >
                  <Input placeholder="輸入電話號碼" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="email"
                  label="邮箱 / EMAIL"
                >
                  <Input placeholder="輸入電子郵件" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="payment_terms"
                  label="帳期 / NGÀY CÔNG NỢ (天)"
                  rules={[{ required: true, message: '請輸入帳期天數' }]}
                >
                  <InputNumber style={{ width: '100%' }} placeholder="30" min={0} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="contract_info"
              label="合約 / HỢP ĐỒNG"
            >
              <TextArea rows={2} placeholder="輸入合約資訊" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="contract_returned"
                  label="ĐÃ NHẬN LẠI HỢP ĐỒNG 合約已取回"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="已取回" unCheckedChildren="未取回" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="credit_limit"
                  label="信用額度"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="0"
                    min={0}
                    formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="备注 / GHI CHÚ"
            >
              <TextArea rows={3} placeholder="輸入備註" />
            </Form.Item>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default CustomerData;
