import React from 'react';
import { Form, Input, Button, Card, Typography, Space, Dropdown, message } from 'antd';
import { UserOutlined, LockOutlined, GlobalOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const { Title } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [form] = Form.useForm();

  // 語言切換選單
  const languageMenu = {
    items: [
      {
        key: 'zh-TW',
        label: '繁體中文',
        onClick: () => i18n.changeLanguage('zh-TW'),
      },
      {
        key: 'vi-VN',
        label: 'Tiếng Việt',
        onClick: () => i18n.changeLanguage('vi-VN'),
      },
    ],
  };

  const onFinish = async (values: LoginForm) => {
    try {
      console.log('Login data:', values);

      // 調用後端登入 API
      const formData = new FormData();
      formData.append('username', values.username);
      formData.append('password', values.password);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/token`, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Login successful, received data:', data);
        // 儲存 token 和用戶資訊
        localStorage.setItem('token', data.access_token);
        localStorage.setItem('user', JSON.stringify(data.user));
        message.success(t('auth.loginSuccess'));
        navigate('/dashboard');
      } else {
        const errorData = await response.json();
        console.error('Login failed, error details:', errorData);
        message.error(errorData.detail || t('auth.loginFailed'));
      }
    } catch (error) {
      console.error('Login error:', error);
      message.error(t('messages.networkError'));
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card
        style={{
          width: 400,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            {t('dashboard.companyName')} ERP {t('common.system')}
          </Title>
          <div style={{ color: '#666', fontSize: '14px' }}>
            Shun Hsing Company ERP System
          </div>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              {
                required: true,
                message: t('auth.invalidCredentials'),
              },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('auth.username')}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: t('auth.invalidCredentials'),
              },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('auth.password')}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: '100%' }}
            >
              {t('auth.login')}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Space>
            <span style={{ color: '#666', fontSize: '12px' }}>
              {t('common.language')} / Language:
            </span>
            <Dropdown menu={languageMenu} placement="top">
              <Button type="link" icon={<GlobalOutlined />} size="small">
                {i18n.language === 'vi-VN' ? 'Tiếng Việt' : '繁體中文'}
              </Button>
            </Dropdown>
          </Space>
        </div>

        <div style={{ 
          textAlign: 'center', 
          marginTop: 24, 
          padding: '12px',
          background: '#f5f5f5',
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          <div>{t('auth.testAccount')} / Test Account:</div>
          <div>{t('auth.username')} / Username: <strong>admin</strong></div>
          <div>{t('auth.password')} / Password: <strong>admin123</strong></div>
        </div>
      </Card>
    </div>
  );
};

export default Login;
