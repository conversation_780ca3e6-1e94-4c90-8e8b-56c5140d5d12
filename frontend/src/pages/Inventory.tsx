import React, { useState, useEffect } from 'react';
import { Card, Typography, Table, Tag, Progress, Space, Button, Tabs, Row, Col, Statistic, Alert, Divider, Modal, message } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, 
  FileExcelOutlined, FilterOutlined, HistoryOutlined, <PERSON><PERSON><PERSON>Outlined, WarningOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import SearchFilters from '../components/SearchFilters';
import FormModal from '../components/FormModal';
import DetailModal from '../components/DetailModal';
import { inventoryAPI } from '../services/api';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

// 自定義組件類型
interface FilterField {
  key: string;
  label: string;
  type: 'search' | 'select' | 'dateRange';
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  span?: number;
}

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'date' | 'textarea' | 'email' | 'phone';
  required?: boolean;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  span?: number;
  placeholder?: string;
  disabled?: boolean;
}

interface DetailField {
  label: string;
  key: string;
  type?: 'text' | 'date' | 'currency' | 'tag' | 'custom';
  render?: (value: any, record: any) => React.ReactNode;
  span?: number;
}

interface InventoryItem {
  id: number;
  item_code: string;
  item_name: string;
  category: string;
  unit: string;
  current_stock: number;
  min_stock: number;
  max_stock: number;
  unit_cost: number;
  total_value: number;
  location: string;
  supplier: string;
  created_at: string;
  updated_at: string;
}

interface StockTransaction {
  id: number;
  item_id: number;
  item_name: string;
  transaction_type: 'in' | 'out' | 'adjust';
  quantity: number;
  unit_cost: number;
  reference_no?: string;
  notes?: string;
  created_at: string;
}

interface StockLevelReport {
  total_items: number;
  low_stock_items: number;
  excess_stock_items: number;
  normal_stock_items: number;
  total_value: number;
  category_statistics: CategoryStat[];
}

interface CategoryStat {
  category: string;
  count: number;
  value: number;
}

const Inventory: React.FC = () => {
  const { t } = useTranslation();
  
  // 數據狀態
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [transactions, setTransactions] = useState<StockTransaction[]>([]);
  const [stockReport, setStockReport] = useState<StockLevelReport | null>(null);
  const [lowStockAlerts, setLowStockAlerts] = useState<any[]>([]);
  
  // 過濾狀態
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<Record<string, any>>({});
  
  // 頁籤狀態
  const [activeTab, setActiveTab] = useState<string>('items');
  
  // 模態框狀態
  const [itemFormVisible, setItemFormVisible] = useState<boolean>(false);
  const [transactionFormVisible, setTransactionFormVisible] = useState<boolean>(false);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [currentItem, setCurrentItem] = useState<InventoryItem | null>(null);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  
  // 過濾條件
  const [filterOptions, setFilterOptions] = useState({
    categories: [] as string[],
    suppliers: [] as string[],
    locations: [] as string[]
  });

  // 統計數據
  const [stats, setStats] = useState({
    totalItems: 0,
    totalValue: 0,
    lowStockItems: 0,
    excessStockItems: 0
  });

  // 獲取庫存項目
  const fetchInventoryItems = async () => {
    setLoading(true);
    try {
      const params: any = {};

      if (filters.category) {
        params.category = filters.category;
      }

      if (filters.low_stock) {
        params.low_stock = true;
      }

      const response = await inventoryAPI.getItems(params);
      setInventoryItems(response.data);
    } catch (error) {
      console.error('Failed to fetch inventory items:', error);
      message.error(t('messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 獲取交易記錄
  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const params: any = {};
      
      if (filters.transactionType) {
        params.transaction_type = filters.transactionType;
      }
      
      if (filters.item_id) {
        params.item_id = filters.item_id;
      }
      
      const response = await inventoryAPI.getTransactions(params);
      setTransactions(response.data);
    } catch (error) {
      console.error('Failed to fetch inventory transactions:', error);
      message.error(t('inventory.fetchTransactionsFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 獲取統計報表
  const fetchStockReport = async () => {
    try {
      const response = await inventoryAPI.getStockLevelReport();
      setStockReport(response.data);
      
      // 更新統計數據
      setStats({
        totalItems: response.data.total_items,
        totalValue: response.data.total_value,
        lowStockItems: response.data.low_stock_items,
        excessStockItems: response.data.excess_stock_items
      });
    } catch (error) {
      console.error('Failed to fetch stock report:', error);
    }
  };

  // 獲取低庫存警報
  const fetchLowStockAlerts = async () => {
    try {
      const response = await inventoryAPI.getLowStockAlerts();
      setLowStockAlerts(response.data);
    } catch (error) {
      console.error('Failed to fetch low stock alerts:', error);
    }
  };

  // 獲取過濾選項
  const fetchFilterOptions = async () => {
    try {
      const [categoriesRes, suppliersRes, locationsRes] = await Promise.all([
        inventoryAPI.getCategories(),
        inventoryAPI.getSuppliers(),
        inventoryAPI.getLocations()
      ]);
      
      setFilterOptions({
        categories: categoriesRes.data,
        suppliers: suppliersRes.data,
        locations: locationsRes.data
      });
    } catch (error) {
      console.error('Failed to fetch filter options:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchInventoryItems();
    fetchFilterOptions();
    fetchStockReport();
    fetchLowStockAlerts();
  }, []);

  // 頁籤切換時加載相應數據
  useEffect(() => {
    if (activeTab === 'items') {
      fetchInventoryItems();
    } else if (activeTab === 'transactions') {
      fetchTransactions();
    } else if (activeTab === 'analytics') {
      fetchStockReport();
    }
  }, [activeTab]);

  // 過濾條件變化時重新加載數據
  useEffect(() => {
    if (activeTab === 'items') {
      fetchInventoryItems();
    } else if (activeTab === 'transactions') {
      fetchTransactions();
    }
  }, [filters]);

  // 處理表單提交 - 新增或更新庫存項目
  const handleItemFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 轉換命名規範從駝峰式到蛇形式
      const data = {
        item_code: values.itemCode,
        item_name: values.itemName,
        category: values.category,
        unit: values.unit,
        current_stock: values.currentStock,
        min_stock: values.minStock,
        max_stock: values.maxStock,
        unit_cost: values.unitCost,
        location: values.location,
        supplier: values.supplier
      };
      
      if (editMode && currentItem) {
        // 更新項目
        await inventoryAPI.updateItem(currentItem.id, data);
        message.success(t('inventory.itemUpdated'));
      } else {
        // 新增項目
        await inventoryAPI.createItem(data);
        message.success(t('inventory.itemAdded'));
      }
      
      setItemFormVisible(false);
      setCurrentItem(null);
      
      // 重新獲取數據
      fetchInventoryItems();
      fetchStockReport();
    } catch (error) {
      message.error(t('messages.operationFailed'));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 處理庫存異動記錄提交
  const handleTransactionFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 轉換命名規範
      const data = {
        item_id: values.itemId,
        transaction_type: values.transactionType,
        quantity: values.quantity,
        unit_cost: values.unitCost,
        reference_no: values.referenceNo,
        notes: values.notes
      };
      
      // 新增交易記錄
      await inventoryAPI.createTransaction(data);
      
      message.success(t('inventory.transactionRecorded'));
      setTransactionFormVisible(false);
      
      // 重新獲取數據
      fetchInventoryItems();
      fetchTransactions();
      fetchStockReport();
    } catch (error) {
      message.error(t('messages.operationFailed'));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除庫存項目
  const handleDeleteItem = (id: number) => {
    Modal.confirm({
      title: t('inventory.confirmDelete'),
      content: t('inventory.deleteConfirmContent'),
      okText: t('inventory.confirmDeleteButton'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk() {
        return new Promise((resolve, reject) => {
          inventoryAPI.deleteItem(id)
            .then((response) => {
              console.log('Delete response:', response);
              message.success(t('inventory.itemDeleted'));
              return Promise.all([
                fetchInventoryItems(),
                fetchStockReport()
              ]);
            })
            .then(() => {
              console.log('Data refresh completed');
              resolve(true);
            })
            .catch((error) => {
              console.error('Delete failed, error details:', error);
              message.error(t('inventory.deleteFailed', { error: error.message || t('common.unknownError') }));
              reject(error);
            });
        });
      },
      onCancel() {
        console.log('User cancelled deletion');
      }
    });
  };

  // 顯示項目詳情
  const showItemDetails = (item: InventoryItem) => {
    setCurrentItem(item);
    setDetailModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = async (item: InventoryItem) => {
    try {
      // Get latest item data
      const response = await inventoryAPI.getItem(item.id);
      const latestItem = response.data;
      
      // Convert naming convention from snake_case to camelCase
      const formattedItem = {
        id: latestItem.id,
        itemCode: latestItem.item_code,
        itemName: latestItem.item_name,
        category: latestItem.category,
        unit: latestItem.unit,
        currentStock: latestItem.current_stock,
        minStock: latestItem.min_stock,
        maxStock: latestItem.max_stock,
        unitCost: latestItem.unit_cost,
        totalValue: latestItem.total_value,
        location: latestItem.location,
        supplier: latestItem.supplier
      };
      
      setCurrentItem(formattedItem as any);
      setEditMode(true);
      setItemFormVisible(true);
    } catch (error) {
      message.error(t('inventory.fetchItemDetailsFailed'));
      console.error(error);
    }
  };

  // 顯示新增表單
  const showAddForm = () => {
    setCurrentItem(null);
    setEditMode(false);
    setItemFormVisible(true);
  };

  // Show transaction form
  const showTransactionForm = () => {
    setTransactionFormVisible(true);
  };

  // Set filters
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({...prev, [key]: value}));
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  // Define stock status labels and colors
  const getStockStatus = (current: number, min: number, max: number) => {
    if (current <= min) return { color: 'red', text: t('inventory.lowStock') };
    if (current >= max) return { color: 'orange', text: t('inventory.overStock') };
    return { color: 'green', text: t('inventory.normalStock') };
  };

  // Search filtering
  const filteredInventoryItems = inventoryItems.filter(item => {
    if (!searchTerm) return true;
    
    const searchTermLower = searchTerm.toLowerCase();
    return (
      item.item_code.toLowerCase().includes(searchTermLower) ||
      item.item_name.toLowerCase().includes(searchTermLower) ||
      item.category.toLowerCase().includes(searchTermLower) ||
      item.supplier.toLowerCase().includes(searchTermLower) ||
      item.location.toLowerCase().includes(searchTermLower)
    );
  });

  const filteredTransactions = transactions.filter(transaction => {
    if (!searchTerm) return true;
    
    const searchTermLower = searchTerm.toLowerCase();
    return (
      transaction.item_name.toLowerCase().includes(searchTermLower) ||
      (transaction.reference_no && transaction.reference_no.toLowerCase().includes(searchTermLower)) ||
      (transaction.notes && transaction.notes.toLowerCase().includes(searchTermLower))
    );
  });

  // Inventory items table column definitions
  const itemColumns: ColumnsType<InventoryItem> = [
    {
      title: t('inventory.itemCode'),
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      sorter: (a: InventoryItem, b: InventoryItem) => a.item_code.localeCompare(b.item_code),
    },
    {
      title: t('inventory.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
      sorter: (a: InventoryItem, b: InventoryItem) => a.item_name.localeCompare(b.item_name),
    },
    {
      title: t('inventory.category'),
      dataIndex: 'category',
      key: 'category',
      width: 100,
      filters: filterOptions.categories.map(cat => ({ text: cat, value: cat })),
      onFilter: (value: any, record: InventoryItem) => record.category === value,
    },
    {
      title: t('inventory.currentStock'),
      dataIndex: 'current_stock',
      key: 'current_stock',
      width: 120,
      sorter: (a: InventoryItem, b: InventoryItem) => a.current_stock - b.current_stock,
      render: (stock: number, record: InventoryItem) => (
        <span>{stock} {record.unit}</span>
      ),
    },
    {
      title: t('inventory.stockStatus'),
      key: 'status',
      width: 100,
      render: (_: any, record: InventoryItem) => {
        const status = getStockStatus(record.current_stock, record.min_stock, record.max_stock);
        return <Tag color={status.color}>{status.text}</Tag>;
      },
    },
    {
      title: t('inventory.stockLevel'),
      key: 'stockLevel',
      width: 150,
      render: (_: any, record: InventoryItem) => {
        const percentage = (record.current_stock / record.max_stock) * 100;
        const status = getStockStatus(record.current_stock, record.min_stock, record.max_stock);
        return (
          <Progress
            percent={Math.min(percentage, 100)}
            size="small"
            strokeColor={status.color}
            showInfo={false}
          />
        );
      },
    },
    {
      title: t('inventory.unitCost'),
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 100,
      sorter: (a: InventoryItem, b: InventoryItem) => a.unit_cost - b.unit_cost,
      render: (cost: number) => `${cost.toLocaleString()} VND`,
    },
    {
      title: t('inventory.totalValue'),
      dataIndex: 'total_value',
      key: 'total_value',
      width: 120,
      sorter: (a: InventoryItem, b: InventoryItem) => a.total_value - b.total_value,
      render: (value: number) => `${value.toLocaleString()} VND`,
    },
    {
      title: t('inventory.location'),
      dataIndex: 'location',
      key: 'location',
      width: 100,
      filters: filterOptions.locations.map(loc => ({ text: loc, value: loc })),
      onFilter: (value: any, record: InventoryItem) => record.location === value,
    },
    {
      title: t('inventory.lastUpdated'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: t('inventory.operations'),
      key: 'actions',
      width: 150,
      render: (_: any, record: InventoryItem) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} title={t('inventory.viewDetails')} onClick={() => showItemDetails(record)} />
          <Button type="text" icon={<EditOutlined />} title={t('inventory.edit')} onClick={() => showEditForm(record)} />
          <Button type="text" danger icon={<DeleteOutlined />} title={t('common.delete')} onClick={() => handleDeleteItem(record.id)} />
        </Space>
      ),
    },
  ];

  // Transaction records table column definitions
  const transactionColumns: ColumnsType<StockTransaction> = [
    {
      title: t('inventory.time'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      sorter: (a: StockTransaction, b: StockTransaction) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: t('inventory.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
    },
    {
      title: t('inventory.transactionType'),
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      width: 100,
      render: (type: string) => {
        const types = {
          in: { text: t('inventory.stockIn'), color: 'green' },
          out: { text: t('inventory.stockOut'), color: 'red' },
          adjust: { text: t('inventory.stockAdjust'), color: 'blue' },
        };
        const typeInfo = (types as any)[type] || { text: type, color: 'default' };
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
      },
      filters: [
        { text: t('inventory.stockIn'), value: 'in' },
        { text: t('inventory.stockOut'), value: 'out' },
        { text: t('inventory.stockAdjust'), value: 'adjust' },
      ],
      onFilter: (value: any, record: StockTransaction) => record.transaction_type === value,
    },
    {
      title: t('inventory.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (qty: number, record: StockTransaction) => {
        const item = inventoryItems.find(i => i.id === record.item_id);
        const unit = item ? item.unit : '';
        return <span>{qty} {unit}</span>;
      },
    },
    {
      title: t('inventory.unitCost'),
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 100,
      render: (cost: number) => `${cost.toLocaleString()} VND`,
    },
    {
      title: t('inventory.totalAmount'),
      key: 'totalAmount',
      width: 120,
      render: (_: any, record: StockTransaction) =>
        `${(record.quantity * record.unit_cost).toLocaleString()} VND`,
    },
    {
      title: t('inventory.referenceNumber'),
      dataIndex: 'reference_no',
      key: 'reference_no',
      width: 120,
    },
    {
      title: t('inventory.notes'),
      dataIndex: 'notes',
      key: 'notes',
    },
  ];

  // Inventory item form fields
  const itemFormFields: FormField[] = [
    { name: 'itemCode', label: t('inventory.itemCode'), type: 'text', required: true },
    { name: 'itemName', label: t('inventory.itemName'), type: 'text', required: true },
    { name: 'category', label: t('inventory.category'), type: 'select', required: true,
      options: filterOptions.categories.map(cat => ({ label: cat, value: cat })) },
    { name: 'unit', label: t('inventory.unit'), type: 'text', required: true },
    { name: 'currentStock', label: t('inventory.currentStock'), type: 'number', required: true },
    { name: 'minStock', label: t('inventory.minStock'), type: 'number', required: true },
    { name: 'maxStock', label: t('inventory.maxStock'), type: 'number', required: true },
    { name: 'unitCost', label: t('inventory.unitCostVND'), type: 'number', required: true },
    { name: 'location', label: t('inventory.location'), type: 'text', required: true },
    { name: 'supplier', label: t('inventory.supplier'), type: 'select',
      options: filterOptions.suppliers.map(sup => ({ label: sup, value: sup })) },
  ];

  // Transaction record form fields
  const transactionFormFields: FormField[] = [
    { name: 'itemId', label: t('inventory.material'), type: 'select', required: true,
      options: inventoryItems.map(item => ({ label: `${item.item_code} - ${item.item_name}`, value: item.id })) },
    { name: 'transactionType', label: t('inventory.transactionType'), type: 'select', required: true,
      options: [
        { label: t('inventory.stockIn'), value: 'in' },
        { label: t('inventory.stockOut'), value: 'out' },
        { label: t('inventory.stockAdjust'), value: 'adjust' },
      ] },
    { name: 'quantity', label: t('inventory.quantity'), type: 'number', required: true },
    { name: 'unitCost', label: t('inventory.unitCostVND'), type: 'number', required: true },
    { name: 'referenceNo', label: t('inventory.referenceNumber'), type: 'text' },
    { name: 'notes', label: t('inventory.notes'), type: 'textarea' },
  ];

  // Detail view fields
  const detailFields: DetailField[] = [
    { label: t('inventory.itemCode'), key: 'item_code' },
    { label: t('inventory.itemName'), key: 'item_name' },
    { label: t('inventory.category'), key: 'category' },
    { label: t('inventory.unit'), key: 'unit' },
    { label: t('inventory.currentStock'), key: 'current_stock', render: (val: number, record: InventoryItem) => `${val} ${record.unit}` },
    { label: t('inventory.minStock'), key: 'min_stock', render: (val: number, record: InventoryItem) => `${val} ${record.unit}` },
    { label: t('inventory.maxStock'), key: 'max_stock', render: (val: number, record: InventoryItem) => `${val} ${record.unit}` },
    { label: t('inventory.unitCost'), key: 'unit_cost', type: 'currency' },
    { label: t('inventory.totalValue'), key: 'total_value', type: 'currency' },
    { label: t('inventory.location'), key: 'location' },
    { label: t('inventory.supplier'), key: 'supplier' },
    { label: t('inventory.createdAt'), key: 'created_at', type: 'date' },
    { label: t('inventory.lastUpdated'), key: 'updated_at', type: 'date' },
    { label: t('inventory.stockStatus'), key: 'status', render: (_: any, record: InventoryItem) => {
      const status = getStockStatus(record.current_stock, record.min_stock, record.max_stock);
      return <Tag color={status.color}>{status.text}</Tag>;
    }},
  ];

  // Search filter fields
  const inventoryFilterFields: FilterField[] = [
    { key: 'search', label: t('common.search'), type: 'search' },
    { key: 'category', label: t('inventory.category'), type: 'select',
      options: filterOptions.categories.map(cat => ({ label: cat, value: cat })) },
    { key: 'supplier', label: t('inventory.supplier'), type: 'select',
      options: filterOptions.suppliers.map(sup => ({ label: sup, value: sup })) },
    { key: 'location', label: t('inventory.location'), type: 'select',
      options: filterOptions.locations.map(loc => ({ label: loc, value: loc })) },
  ];

  // Transaction record search filter fields
  const transactionFilterFields: FilterField[] = [
    { key: 'search', label: t('common.search'), type: 'search' },
    { key: 'transactionType', label: t('inventory.transactionType'), type: 'select',
      options: [
        { label: t('inventory.stockIn'), value: 'in' },
        { label: t('inventory.stockOut'), value: 'out' },
        { label: t('inventory.stockAdjust'), value: 'adjust' },
      ] },
    { key: 'dateRange', label: t('inventory.dateRange'), type: 'dateRange' },
  ];

  return (
    <div>
      <Title level={2}>{t('inventory.title')}</Title>
      
      {/* 庫存統計卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('inventory.totalItems')}
              value={stats.totalItems}
              prefix={<FilterOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('inventory.totalValue')}
              value={stats.totalValue}
              precision={0}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('inventory.lowStockItems')}
              value={stats.lowStockItems}
              valueStyle={{ color: '#cf1322' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('inventory.excessStockItems')}
              value={stats.excessStockItems}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      {/* Low stock warning */}
      {stats.lowStockItems > 0 && (
        <Alert
          message={t('inventory.stockWarning')}
          description={t('inventory.lowStockWarningMessage', { count: stats.lowStockItems })}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'items',
            label: t('inventory.inventoryItems'),
            children: (
          <Card>
            {/* 搜尋過濾 */}
            <SearchFilters
              fields={inventoryFilterFields}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClear={handleClearFilters}
              onAdd={showAddForm}
              addButtonText={t('inventory.addMaterial')}
            />

            <Space style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<HistoryOutlined />}
                onClick={showTransactionForm}
              >
                {t('inventory.recordTransaction')}
              </Button>
              <Button
                icon={<FileExcelOutlined />}
                onClick={() => message.info(t('inventory.exportNotImplemented'))}
              >
                {t('inventory.exportData')}
              </Button>
              <Button
                icon={<SyncOutlined />}
                onClick={fetchInventoryItems}
              >
                {t('common.refreshData')}
              </Button>
            </Space>

            <Table
              columns={itemColumns}
              dataSource={filteredInventoryItems}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
              }}
            />
          </Card>
            )
          },
          {
            key: 'transactions',
            label: t('inventory.transactionRecords'),
            children: (
          <Card>
            {/* 異動記錄搜尋過濾 */}
            <SearchFilters
              fields={transactionFilterFields}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClear={handleClearFilters}
              onAdd={showTransactionForm}
              addButtonText={t('inventory.recordTransaction')}
            />
            
            <Button
              icon={<SyncOutlined />}
              style={{ marginBottom: 16 }}
              onClick={fetchTransactions}
            >
              {t('common.refreshData')}
            </Button>
            
            <Table
              columns={transactionColumns}
              dataSource={filteredTransactions}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
              }}
            />
          </Card>
            )
          },
          {
            key: 'analytics',
            label: t('inventory.stockAnalysis'),
            children: (
          <Card>
            <Button
              icon={<SyncOutlined />}
              style={{ marginBottom: 16 }}
              onClick={fetchStockReport}
            >
              {t('common.refreshData')}
            </Button>
            
            <Row gutter={16}>
              <Col span={12}>
                <Card title={t('inventory.categoryDistribution')}>
                  <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    {stockReport && stockReport.category_statistics.length > 0 ? (
                      <div>{t('inventory.categoryDataLoaded')}</div>
                    ) : (
                      <Text type="secondary">{t('inventory.categoryChartPlaceholder')}</Text>
                    )}
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title={t('inventory.valueTrend')}>
                  <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Text type="secondary">{t('inventory.valueTrendChartPlaceholder')}</Text>
                  </div>
                </Card>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <Card title={t('inventory.turnoverRate')}>
                  <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Text type="secondary">{t('inventory.turnoverRateChartPlaceholder')}</Text>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title={t('inventory.lowStockAlert')}>
                  <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    {lowStockAlerts && lowStockAlerts.length > 0 ? (
                      <Table
                        dataSource={lowStockAlerts}
                        rowKey="id"
                        size="small"
                        pagination={false}
                        columns={[
                          { title: t('inventory.itemName'), dataIndex: 'item_name' },
                          { title: t('inventory.currentStock'), dataIndex: 'current_stock' },
                          { title: t('inventory.minStock'), dataIndex: 'min_stock' },
                          { title: t('inventory.shortage'), dataIndex: 'shortage' }
                        ]}
                      />
                    ) : (
                      <Text type="secondary">{t('inventory.noLowStockAlerts')}</Text>
                    )}
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
            )
          }
        ]}
      />
      
      {/* 物料表單對話框 */}
      <FormModal
        title={editMode ? t('inventory.editMaterial') : t('inventory.addMaterial')}
        visible={itemFormVisible}
        onCancel={() => setItemFormVisible(false)}
        onOk={handleItemFormSubmit}
        fields={itemFormFields}
        initialValues={currentItem || undefined}
        loading={loading}
      />
      
      {/* 異動記錄表單對話框 */}
      <FormModal
        title={t('inventory.transactionRecord')}
        visible={transactionFormVisible}
        onCancel={() => setTransactionFormVisible(false)}
        onOk={handleTransactionFormSubmit}
        fields={transactionFormFields}
        loading={loading}
      />
      
      {/* 詳情對話框 */}
      <DetailModal
        title={t('inventory.materialDetails')}
        visible={detailModalVisible}
        onClose={() => setDetailModalVisible(false)}
        data={currentItem || {}}
        fields={detailFields}
      />
    </div>
  );
};

export default Inventory;
