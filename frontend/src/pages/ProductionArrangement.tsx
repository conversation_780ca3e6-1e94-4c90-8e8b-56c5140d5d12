import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Statistic,
  Progress
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  DragOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { productionArrangementAPI, type ProductionArrangement, type ProductionArrangementCreate, type ProductionArrangementUpdate, type ProductionSummary, type CustomerOption } from '../services/productionArrangementAPI';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const ProductionArrangementPage: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [arrangements, setArrangements] = useState<ProductionArrangement[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentArrangement, setCurrentArrangement] = useState<ProductionArrangement | null>(null);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [materials, setMaterials] = useState<string[]>([]);
  const [summary, setSummary] = useState<ProductionSummary | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取生產安排列表
  const fetchArrangements = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { production_status: statusFilter }),
        ...(priorityFilter && { priority: priorityFilter }),
        ...(customerFilter && { customer_name: customerFilter }),
      };

      const response = await productionArrangementAPI.getProductionArrangements(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setArrangements(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch production arrangements:', error);
      message.error('獲取生產安排資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取統計資料和元數據
  const fetchMetadata = async () => {
    try {
      const [customersRes, materialsRes, summaryRes] = await Promise.all([
        productionArrangementAPI.getCustomersList(),
        productionArrangementAPI.getMaterialsList(),
        productionArrangementAPI.getProductionSummary()
      ]);
      setCustomers(customersRes.data);
      setMaterials(materialsRes.data);
      setSummary(summaryRes.data);
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchArrangements();
    fetchMetadata();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchArrangements(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setStatusFilter('');
    setPriorityFilter('');
    setCustomerFilter('');
    setTimeout(() => {
      fetchArrangements(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchArrangements(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentArrangement(null);
    form.resetFields();
    form.setFieldsValue({
      production_status: 'pending',
      priority: 'normal',
      scheduled_date: dayjs()
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (arrangement: ProductionArrangement) => {
    setEditMode(true);
    setCurrentArrangement(arrangement);
    form.setFieldsValue({
      ...arrangement,
      scheduled_date: arrangement.scheduled_date ? dayjs(arrangement.scheduled_date) : null,
      start_date: arrangement.start_date ? dayjs(arrangement.start_date) : null,
      completion_date: arrangement.completion_date ? dayjs(arrangement.completion_date) : null,
    });
    setModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      const arrangementData = {
        ...values,
        scheduled_date: values.scheduled_date?.format('YYYY-MM-DD'),
        start_date: values.start_date?.format('YYYY-MM-DD'),
        completion_date: values.completion_date?.format('YYYY-MM-DD'),
      };

      if (editMode && currentArrangement) {
        // 更新生產安排
        await productionArrangementAPI.updateProductionArrangement(currentArrangement.id, arrangementData as ProductionArrangementUpdate);
        message.success('生產安排更新成功');
      } else {
        // 新增生產安排
        await productionArrangementAPI.createProductionArrangement(arrangementData as ProductionArrangementCreate);
        message.success('生產安排新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchArrangements(pagination.current, pagination.pageSize);
      fetchMetadata(); // 重新獲取統計資料
    } catch (error: any) {
      console.error('Failed to save production arrangement:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除生產安排
  const handleDelete = async (arrangement: ProductionArrangement) => {
    try {
      await productionArrangementAPI.deleteProductionArrangement(arrangement.id);
      message.success('生產安排已刪除');
      fetchArrangements(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error) {
      console.error('Failed to delete production arrangement:', error);
      message.error('刪除失敗');
    }
  };

  // 開始生產
  const handleStartProduction = async (arrangement: ProductionArrangement) => {
    try {
      await productionArrangementAPI.startProduction(arrangement.id);
      message.success('生產已開始');
      fetchArrangements(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error) {
      console.error('Failed to start production:', error);
      message.error('開始生產失敗');
    }
  };

  // 完成生產
  const handleCompleteProduction = async (arrangement: ProductionArrangement) => {
    Modal.confirm({
      title: '完成生產',
      content: (
        <div>
          <p>確定要完成這個生產項目嗎？</p>
          <Form>
            <Form.Item label="實際數量">
              <InputNumber
                id="actual-quantity"
                min={0}
                placeholder="輸入實際生產數量"
                defaultValue={arrangement.ordered_quantity}
              />
            </Form.Item>
          </Form>
        </div>
      ),
      onOk: async () => {
        try {
          const actualQuantityInput = document.getElementById('actual-quantity') as HTMLInputElement;
          const actualQuantity = actualQuantityInput ? parseInt(actualQuantityInput.value) : arrangement.ordered_quantity;
          
          await productionArrangementAPI.completeProduction(arrangement.id, actualQuantity);
          message.success('生產已完成');
          fetchArrangements(pagination.current, pagination.pageSize);
          fetchMetadata();
        } catch (error) {
          console.error('Failed to complete production:', error);
          message.error('完成生產失敗');
        }
      }
    });
  };

  // 表格欄位定義
  const columns = [
    {
      title: 'STT',
      dataIndex: 'sequence_no',
      key: 'sequence_no',
      width: 60,
      fixed: 'left' as const,
      sorter: (a: ProductionArrangement, b: ProductionArrangement) => (a.sequence_no || 0) - (b.sequence_no || 0),
    },
    {
      title: '客戶 / Khách hàng',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 150,
    },
    {
      title: '訂單編號 / MÃ ĐƠN ĐẶT HÀNG',
      dataIndex: 'order_code',
      key: 'order_code',
      width: 150,
    },
    {
      title: '代号 / Chất liệu',
      dataIndex: 'material_code',
      key: 'material_code',
      width: 120,
    },
    {
      title: '切宽 / Cắt rộng (mm)',
      dataIndex: 'cut_width',
      key: 'cut_width',
      width: 120,
      render: (width: number) => width ? `${width}mm` : '-',
    },
    {
      title: '切长 / Cắt dài (mm)',
      dataIndex: 'cut_length',
      key: 'cut_length',
      width: 120,
      render: (length: number) => length ? `${length}mm` : '-',
    },
    {
      title: '小张数 / Số tấm nhỏ',
      dataIndex: 'small_sheets',
      key: 'small_sheets',
      width: 120,
    },
    {
      title: '瓦楞 / SÓNG',
      dataIndex: 'corrugated',
      key: 'corrugated',
      width: 100,
    },
    {
      title: '压线 / CÁN LẰN',
      dataIndex: 'pressing_lines',
      key: 'pressing_lines',
      width: 100,
    },
    {
      title: '產品名稱 / Tên hàng hóa',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '產品名稱-規格 / Tên hàng - quy cách',
      dataIndex: 'product_name_with_spec',
      key: 'product_name_with_spec',
      width: 250,
      ellipsis: true,
    },
    {
      title: '訂单数量 / SL khách đặt',
      dataIndex: 'ordered_quantity',
      key: 'ordered_quantity',
      width: 120,
    },
    {
      title: '實際數量 / SL thực tế',
      dataIndex: 'actual_quantity',
      key: 'actual_quantity',
      width: 120,
      render: (quantity: number, record: ProductionArrangement) => {
        if (record.production_status === 'completed' && quantity) {
          return <Text style={{ color: 'green' }}>{quantity}</Text>;
        }
        return quantity || '-';
      },
    },
    {
      title: '狀態',
      dataIndex: 'production_status',
      key: 'production_status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'orange', text: '待生產' },
          in_progress: { color: 'blue', text: '生產中' },
          completed: { color: 'green', text: '已完成' },
          cancelled: { color: 'red', text: '已取消' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '優先級',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => {
        const priorityMap = {
          high: { color: 'red', text: '高' },
          normal: { color: 'blue', text: '中' },
          low: { color: 'green', text: '低' }
        };
        const priorityInfo = priorityMap[priority as keyof typeof priorityMap] || { color: 'default', text: priority };
        return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
      },
    },
    {
      title: '备注 / Ghi chú',
      dataIndex: 'notes',
      key: 'notes',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: ProductionArrangement) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          {record.production_status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartProduction(record)}
            >
              開始
            </Button>
          )}
          {record.production_status === 'in_progress' && (
            <Button
              type="link"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => handleCompleteProduction(record)}
            >
              完成
            </Button>
          )}
          <Popconfirm
            title="確定要刪除這個生產安排嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 統計卡片 */}
      {summary && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="總計" value={summary.total} prefix={<BarChartOutlined />} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="待生產" value={summary.pending} valueStyle={{ color: '#faad14' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="生產中" value={summary.in_progress} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="完成率" 
                value={summary.completion_rate} 
                suffix="%" 
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress percent={summary.completion_rate} showInfo={false} />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>生產安排管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜尋訂單編號、產品名稱或材質代號"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="客戶"
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="children"
              value={customerFilter || undefined}
              onChange={(value) => setCustomerFilter(value || '')}
              filterOption={(input, option) => {
                const optionText = option?.children as string;
                return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
              }}
            >
              {customers.map(customer => {
                const displayText = `${customer.customer_code} - ${customer.company_name}`;
                return (
                  <Select.Option
                    key={customer.id}
                    value={customer.company_name}
                  >
                    {displayText}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="生產狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="pending">待生產</Select.Option>
              <Select.Option value="in_progress">生產中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
              <Select.Option value="cancelled">已取消</Select.Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="優先級"
              style={{ width: '100%' }}
              allowClear
              value={priorityFilter || undefined}
              onChange={(value) => setPriorityFilter(value || '')}
            >
              <Select.Option value="high">高</Select.Option>
              <Select.Option value="normal">中</Select.Option>
              <Select.Option value="low">低</Select.Option>
            </Select>
          </Col>
          <Col span={9}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增生產安排
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={arrangements}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 2000 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯生產安排' : '新增生產安排'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={1200}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              production_status: 'pending',
              priority: 'normal'
            }}
          >
            <Row gutter={16}>
              <Col span={4}>
                <Form.Item
                  name="sequence_no"
                  label="STT"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="序號" min={1} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="customer_name"
                  label="客戶 / Khách hàng"
                >
                  <Select
                    placeholder="選擇客戶"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      const optionText = option?.children as string;
                      return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
                    }}
                  >
                    {customers.map(customer => {
                      const displayText = `${customer.customer_code} - ${customer.company_name}${customer.contact_person ? ` (${customer.contact_person})` : ''}`;
                      return (
                        <Select.Option
                          key={customer.id}
                          value={customer.company_name}
                        >
                          {displayText}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="order_code"
                  label="訂單編號 / MÃ ĐƠN ĐẶT HÀNG"
                >
                  <Input placeholder="訂單編號" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="material_code"
                  label="代号 / Chất liệu"
                >
                  <Select
                    placeholder="選擇或輸入材質代號"
                    showSearch
                    allowClear
                    mode="combobox"
                  >
                    {materials.map(material => (
                      <Select.Option key={material} value={material}>{material}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="cut_width"
                  label="切宽 / Cắt rộng (mm)"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="切宽" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="cut_length"
                  label="切长 / Cắt dài (mm)"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="切长" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="small_sheets"
                  label="小张数 / Số tấm nhỏ"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="小张数" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="corrugated"
                  label="瓦楞 / SÓNG"
                >
                  <Input placeholder="瓦楞" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="pressing_lines"
                  label="压线 / CÁN LẰN"
                >
                  <Input placeholder="压线" />
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  name="product_name"
                  label="產品名稱 / Tên hàng hóa"
                >
                  <Input placeholder="產品名稱" />
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  name="product_name_with_spec"
                  label="產品名稱-規格 / Tên hàng - quy cách"
                >
                  <Input placeholder="產品名稱-規格" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="ordered_quantity"
                  label="訂单数量 / SL khách đặt"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="訂单数量" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="actual_quantity"
                  label="實際數量 / SL thực tế"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="實際數量" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="production_status"
                  label="生產狀態"
                >
                  <Select>
                    <Select.Option value="pending">待生產</Select.Option>
                    <Select.Option value="in_progress">生產中</Select.Option>
                    <Select.Option value="completed">已完成</Select.Option>
                    <Select.Option value="cancelled">已取消</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="priority"
                  label="優先級"
                >
                  <Select>
                    <Select.Option value="high">高</Select.Option>
                    <Select.Option value="normal">中</Select.Option>
                    <Select.Option value="low">低</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="scheduled_date"
                  label="排程日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="start_date"
                  label="開始日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="completion_date"
                  label="完成日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="备注 / Ghi chú"
            >
              <TextArea rows={3} placeholder="輸入備註" />
            </Form.Item>

            <Form.Item
              name="created_by"
              label="建立者"
            >
              <Input placeholder="建立者" />
            </Form.Item>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default ProductionArrangementPage;
