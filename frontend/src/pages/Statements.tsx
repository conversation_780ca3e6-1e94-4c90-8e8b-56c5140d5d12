import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  PrinterOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { statementsAPI, type Statement, type StatementCreate, type StatementUpdate, type StatementItem, type CustomerForStatement } from '../services/statementsAPI';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const Statements: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [statements, setStatements] = useState<Statement[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [viewModalVisible, setViewModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentStatement, setCurrentStatement] = useState<Statement | null>(null);
  const [customers, setCustomers] = useState<CustomerForStatement[]>([]);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取對帳單列表
  const fetchStatements = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      };

      const response = await statementsAPI.getStatements(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setStatements(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch statements:', error);
      message.error('獲取對帳單資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取客戶列表
  const fetchCustomers = async () => {
    try {
      const response = await statementsAPI.getCustomersForStatement();
      setCustomers(response.data);
    } catch (error) {
      console.error('Failed to fetch customers:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchStatements();
    fetchCustomers();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchStatements(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setStatusFilter('');
    setTimeout(() => {
      fetchStatements(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchStatements(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentStatement(null);
    form.resetFields();
    form.setFieldsValue({
      tax_rate: 0.08,
      status: 'draft',
      customer_confirmed: false,
      items: [
        {
          sequence_no: 1,
          product_name: 'Khuôn dao VELOUS',
          specifications: '300x200x125mm',
          unit: 'PCS',
          quantity: 1,
          unit_price: 1190000,
          amount: 1190000
        }
      ]
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (statement: Statement) => {
    setEditMode(true);
    setCurrentStatement(statement);
    form.setFieldsValue({
      customer_id: statement.customer_id,
      customer_code: statement.customer_code,
      period: [dayjs(statement.period_start), dayjs(statement.period_end)],
      tax_rate: statement.tax_rate,
      status: statement.status,
      customer_confirmed: statement.customer_confirmed,
      items: statement.items
    });
    setModalVisible(true);
  };

  // 顯示查看詳情
  const showViewModal = (statement: Statement) => {
    setCurrentStatement(statement);
    setViewModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    console.log('Form submitted with values:', values);
    setLoading(true);
    try {
      // 計算金額
      const items = values.items || [];
      const subtotal = items.reduce((sum: number, item: any) => sum + (item.amount || 0), 0);
      const tax_amount = subtotal * (values.tax_rate || 0.08);
      const total_amount = subtotal + tax_amount;

      const statementData = {
        ...values,
        period_start: values.period?.[0]?.format('YYYY-MM-DD'),
        period_end: values.period?.[1]?.format('YYYY-MM-DD'),
        subtotal,
        tax_amount,
        total_amount,
        items: items.map((item: any, index: number) => ({
          ...item,
          sequence_no: index + 1,
          delivery_date: item.delivery_date?.format('YYYY-MM-DD') || null
        }))
      };

      if (editMode && currentStatement) {
        // 更新對帳單
        await statementsAPI.updateStatement(currentStatement.id, statementData as StatementUpdate);
        message.success('對帳單更新成功');
      } else {
        // 新增對帳單
        await statementsAPI.createStatement(statementData as StatementCreate);
        message.success('對帳單新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchStatements(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('Failed to save statement:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除對帳單
  const handleDelete = async (statement: Statement) => {
    try {
      await statementsAPI.deleteStatement(statement.id);
      message.success('對帳單已刪除');
      fetchStatements(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Failed to delete statement:', error);
      message.error('刪除失敗');
    }
  };

  // 列印對帳單
  const handlePrint = (statement: Statement) => {
    setCurrentStatement(statement);
    setTimeout(() => {
      window.print();
    }, 100);
  };

  // 表格欄位定義
  const columns = [
    {
      title: '對帳單號',
      dataIndex: 'statement_no',
      key: 'statement_no',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '客戶代碼',
      dataIndex: 'customer_code',
      key: 'customer_code',
      width: 100,
    },
    {
      title: '客戶名稱',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 200,
    },
    {
      title: '對帳期間',
      key: 'period',
      width: 200,
      render: (_: any, record: Statement) => (
        <span>
          {dayjs(record.period_start).format('YYYY/MM/DD')} - {dayjs(record.period_end).format('YYYY/MM/DD')}
        </span>
      ),
    },
    {
      title: '小計',
      dataIndex: 'subtotal',
      key: 'subtotal',
      width: 120,
      render: (amount: number) => `₫${amount?.toLocaleString() || 0}`,
    },
    {
      title: '稅金',
      dataIndex: 'tax_amount',
      key: 'tax_amount',
      width: 120,
      render: (amount: number) => `₫${amount?.toLocaleString() || 0}`,
    },
    {
      title: '總計',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (amount: number) => `₫${amount?.toLocaleString() || 0}`,
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          draft: { color: 'orange', text: '草稿' },
          sent: { color: 'blue', text: '已發送' },
          confirmed: { color: 'green', text: '已確認' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '客戶確認',
      dataIndex: 'customer_confirmed',
      key: 'customer_confirmed',
      width: 100,
      render: (confirmed: boolean) => (
        <Tag color={confirmed ? 'green' : 'red'}>
          {confirmed ? '已確認' : '未確認'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      fixed: 'right' as const,
      render: (_: any, record: Statement) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showViewModal(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            size="small"
            icon={<PrinterOutlined />}
            onClick={() => handlePrint(record)}
          >
            列印
          </Button>
          <Popconfirm
            title="確定要刪除這個對帳單嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>對帳單管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Input
              placeholder="搜尋對帳單號、客戶代碼或客戶名稱"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="draft">草稿</Select.Option>
              <Select.Option value="sent">已發送</Select.Option>
              <Select.Option value="confirmed">已確認</Select.Option>
            </Select>
          </Col>
          <Col span={14}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增對帳單
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={statements}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯對帳單' : '新增對帳單'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={1200}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            onFinishFailed={(errorInfo) => {
              console.log('Form validation failed:', errorInfo);
              message.error('請檢查表單填寫是否完整');
            }}
            initialValues={{
              tax_rate: 0.08,
              status: 'draft',
              customer_confirmed: false,
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="customer_id"
                  label="客戶"
                  rules={[{ required: true, message: '請選擇客戶' }]}
                >
                  <Select
                    placeholder="選擇客戶"
                    showSearch
                    optionFilterProp="children"
                    onChange={(value) => {
                      const customer = customers.find(c => c.id === value);
                      if (customer) {
                        form.setFieldValue('customer_code', customer.customer_code);
                      }
                    }}
                  >
                    {customers.map(customer => (
                      <Select.Option key={customer.id} value={customer.id}>
                        {customer.customer_code} - {customer.company_name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="customer_code"
                  label="客戶代碼"
                  rules={[{ required: true, message: '請輸入客戶代碼' }]}
                >
                  <Input placeholder="客戶代碼" disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="tax_rate"
                  label="稅率"
                  rules={[{ required: true, message: '請輸入稅率' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0}
                    max={1}
                    step={0.01}
                    formatter={value => `${(Number(value) * 100).toFixed(0)}%`}
                    parser={(value) => {
                      const num = Number(value!.replace('%', '')) / 100;
                      return Math.min(Math.max(num, 0), 1);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="period"
                  label="對帳期間"
                  rules={[{ required: true, message: '請選擇對帳期間' }]}
                >
                  <RangePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="status"
                  label="狀態"
                >
                  <Select>
                    <Select.Option value="draft">草稿</Select.Option>
                    <Select.Option value="sent">已發送</Select.Option>
                    <Select.Option value="confirmed">已確認</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="customer_confirmed"
                  label="客戶確認"
                >
                  <Select>
                    <Select.Option value={false}>未確認</Select.Option>
                    <Select.Option value={true}>已確認</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Divider>對帳單項目</Divider>

            <Form.List name="items">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Row key={key} gutter={8} style={{ marginBottom: 8 }}>
                      <Col span={2}>
                        <Form.Item
                          {...restField}
                          name={[name, 'sequence_no']}
                          label={key === 0 ? '序號' : ''}
                        >
                          <InputNumber min={1} placeholder="序號" />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'delivery_date']}
                          label={key === 0 ? '送貨日期' : ''}
                        >
                          <DatePicker placeholder="送貨日期" />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'delivery_note_no']}
                          label={key === 0 ? '送貨單號' : ''}
                        >
                          <Input placeholder="送貨單號" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'product_name']}
                          label={key === 0 ? '品名' : ''}
                          rules={[{ required: true, message: '請輸入品名' }]}
                        >
                          <Input placeholder="品名" />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'specifications']}
                          label={key === 0 ? '規格' : ''}
                        >
                          <Input placeholder="規格" />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Form.Item
                          {...restField}
                          name={[name, 'unit']}
                          label={key === 0 ? '單位' : ''}
                        >
                          <Input placeholder="單位" />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Form.Item
                          {...restField}
                          name={[name, 'quantity']}
                          label={key === 0 ? '數量' : ''}
                          rules={[{ required: true, message: '請輸入數量' }]}
                        >
                          <InputNumber min={0} placeholder="數量" />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'unit_price']}
                          label={key === 0 ? '單價' : ''}
                          rules={[{ required: true, message: '請輸入單價' }]}
                        >
                          <InputNumber min={0} placeholder="單價" />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'amount']}
                          label={key === 0 ? '金額' : ''}
                          rules={[{ required: true, message: '請輸入金額' }]}
                        >
                          <InputNumber min={0} placeholder="金額" />
                        </Form.Item>
                      </Col>
                      <Col span={1}>
                        {key === 0 && <div style={{ height: '32px', lineHeight: '32px' }}>操作</div>}
                        <Button type="link" onClick={() => remove(name)} danger>
                          刪除
                        </Button>
                      </Col>
                    </Row>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      新增項目
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 查看詳情 Modal */}
        <Modal
          title="對帳單詳情"
          open={viewModalVisible}
          onCancel={() => setViewModalVisible(false)}
          footer={[
            <Button key="print" icon={<PrinterOutlined />} onClick={() => currentStatement && handlePrint(currentStatement)}>
              列印
            </Button>,
            <Button key="close" onClick={() => setViewModalVisible(false)}>
              關閉
            </Button>
          ]}
          width={800}
        >
          {currentStatement && (
            <div>
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Title level={3}>新耀紙箱責任有限公司</Title>
                <Title level={4}>CÔNG TY TNHH NEW SHINE</Title>
                <Text>地址 Địa chỉ: Thửa đất số 390, tờ bản đồ số 27, Ấp 5, Xã mỹ Hạnh, Tỉnh Tây Ninh, Việt Nam</Text><br />
                <Text>税号 MST : 1102041293</Text><br />
                <Text>电话 Điện thoại ：0908929630 Mr.Liao - 0357878890 Ms.Trâm</Text>
              </div>
              
              <Divider />
              
              <div style={{ marginBottom: 16 }}>
                <Title level={4}>Đối chiếu công nợ 應收應付對帳</Title>
                <Text>({dayjs(currentStatement.period_start).format('DD/MM/YYYY')} đến {dayjs(currentStatement.period_end).format('DD/MM/YYYY')})</Text>
              </div>
              
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}>
                  <Text strong>Mã KH: </Text><Text>{currentStatement.customer_code}</Text><br />
                  <Text strong>Khách hàng 客戶: </Text><Text>{currentStatement.customer_name}</Text><br />
                  <Text strong>Mã số thuế 稅號: </Text><Text>{currentStatement.customer_tax_id}</Text><br />
                  <Text strong>Địa chỉ 地址: </Text><Text>{currentStatement.customer_address}</Text><br />
                  <Text strong>Tel: </Text><Text>{currentStatement.customer_phone}</Text>
                </Col>
              </Row>

              <Table
                dataSource={currentStatement.items}
                rowKey="id"
                pagination={false}
                size="small"
                columns={[
                  { title: '序号 STT', dataIndex: 'sequence_no', width: 60 },
                  { title: '送货日期 Ngày giao hàng', dataIndex: 'delivery_date', width: 120, render: (date: string) => date ? dayjs(date).format('DD/MM/YYYY') : '' },
                  { title: '送貨單號 Mã phiếu giao hàng', dataIndex: 'delivery_note_no', width: 120 },
                  { title: '品名 Tên hàng', dataIndex: 'product_name', width: 150 },
                  { title: '規格 Quy cách', dataIndex: 'specifications', width: 120 },
                  { title: '單位 Đơn vị tính', dataIndex: 'unit', width: 80 },
                  { title: '數量 Số lượng', dataIndex: 'quantity', width: 80 },
                  { title: '單價 Đơn giá', dataIndex: 'unit_price', width: 100, render: (price: number) => price?.toLocaleString() },
                  { title: '金額 Thành tiền', dataIndex: 'amount', width: 100, render: (amount: number) => amount?.toLocaleString() },
                  { title: '備註 Ghi chú', dataIndex: 'notes', width: 100 }
                ]}
              />

              <div style={{ marginTop: 16, textAlign: 'right' }}>
                <Text strong>Cộng (小計): ₫{currentStatement.subtotal?.toLocaleString()}</Text><br />
                <Text strong>税金{(currentStatement.tax_rate * 100).toFixed(0)}%: ₫{currentStatement.tax_amount?.toLocaleString()}</Text><br />
                <Text strong style={{ fontSize: '16px' }}>Tổng cộng (總計): ₫{currentStatement.total_amount?.toLocaleString()}</Text>
              </div>

              <Row style={{ marginTop: 24 }}>
                <Col span={12}>
                  <div style={{ textAlign: 'center', border: '1px solid #d9d9d9', padding: '20px' }}>
                    <Text strong>客户确认 XÁC NHẬN CỦA KHÁCH HÀNG</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ textAlign: 'center', border: '1px solid #d9d9d9', padding: '20px' }}>
                    <Text strong>新耀紙箱責任有限公司</Text><br />
                    <Text strong>CÔNG TY TNHH NEW SHINE</Text>
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </Modal>
      </Card>
    </div>
  );
};

export default Statements;
