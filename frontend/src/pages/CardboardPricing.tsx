import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  DollarOutlined,
  ShopOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { cardboardPricingAPI, type CardboardPricing, type CardboardPricingCreate, type CardboardPricingUpdate, type PricingSummary, type SupplierOption, type MaterialOption, type CorrugatedOption } from '../services/cardboardPricingAPI';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const CardboardPricingPage: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [pricingRecords, setPricingRecords] = useState<CardboardPricing[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<CardboardPricing | null>(null);
  const [suppliers, setSuppliers] = useState<SupplierOption[]>([]);
  const [materials, setMaterials] = useState<MaterialOption[]>([]);
  const [corrugatedTypes, setCorrugatedTypes] = useState<CorrugatedOption[]>([]);
  const [summary, setSummary] = useState<PricingSummary | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [supplierFilter, setSupplierFilter] = useState<string>('');
  const [materialFilter, setMaterialFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<any[]>([]);

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取紙板單價列表
  const fetchPricingRecords = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(supplierFilter && { supplier_code: supplierFilter }),
        ...(materialFilter && { material_code: materialFilter }),
        ...(statusFilter && { status: statusFilter }),
        ...(dateRange.length === 2 && {
          date_from: dateRange[0].format('YYYY-MM-DD'),
          date_to: dateRange[1].format('YYYY-MM-DD')
        }),
      };

      const response = await cardboardPricingAPI.getCardboardPricing(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setPricingRecords(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch pricing records:', error);
      message.error('獲取紙板單價資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取統計資料和元數據
  const fetchMetadata = async () => {
    try {
      const [suppliersRes, materialsRes, corrugatedRes, summaryRes] = await Promise.all([
        cardboardPricingAPI.getSuppliersList(),
        cardboardPricingAPI.getMaterialsList(),
        cardboardPricingAPI.getCorrugatedTypesList(),
        cardboardPricingAPI.getPricingSummary()
      ]);
      setSuppliers(suppliersRes.data);
      setMaterials(materialsRes.data);
      setCorrugatedTypes(corrugatedRes.data);
      setSummary(summaryRes.data);
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchPricingRecords();
    fetchMetadata();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchPricingRecords(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setSupplierFilter('');
    setMaterialFilter('');
    setStatusFilter('');
    setDateRange([]);
    setTimeout(() => {
      fetchPricingRecords(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchPricingRecords(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentRecord(null);
    form.resetFields();
    form.setFieldsValue({
      currency: 'VND',
      status: 'active',
      date: dayjs(),
      effective_date: dayjs()
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (record: CardboardPricing) => {
    setEditMode(true);
    setCurrentRecord(record);
    form.setFieldsValue({
      ...record,
      date: record.date ? dayjs(record.date) : null,
      effective_date: record.effective_date ? dayjs(record.effective_date) : null,
      expiry_date: record.expiry_date ? dayjs(record.expiry_date) : null,
    });
    setModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      const pricingData = {
        ...values,
        date: values.date?.format('YYYY-MM-DD'),
        effective_date: values.effective_date?.format('YYYY-MM-DD'),
        expiry_date: values.expiry_date?.format('YYYY-MM-DD'),
      };

      if (editMode && currentRecord) {
        // 更新紙板單價
        await cardboardPricingAPI.updateCardboardPricing(currentRecord.id, pricingData as CardboardPricingUpdate);
        message.success('紙板單價更新成功');
      } else {
        // 新增紙板單價
        await cardboardPricingAPI.createCardboardPricing(pricingData as CardboardPricingCreate);
        message.success('紙板單價新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchPricingRecords(pagination.current, pagination.pageSize);
      fetchMetadata(); // 重新獲取統計資料
    } catch (error: any) {
      console.error('Failed to save pricing record:', error);

      let errorMessage = '操作失敗';

      if (error.response?.status === 422) {
        // 處理 Pydantic 驗證錯誤
        const validationErrors = error.response?.data?.detail;
        if (Array.isArray(validationErrors)) {
          errorMessage = validationErrors.map((err: any) =>
            `${err.loc?.join('.')}: ${err.msg}`
          ).join(', ');
        } else if (typeof validationErrors === 'string') {
          errorMessage = validationErrors;
        } else {
          errorMessage = '資料驗證失敗';
        }
      } else {
        errorMessage = error.response?.data?.detail || error.message || '操作失敗';
      }

      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除紙板單價
  const handleDelete = async (record: CardboardPricing) => {
    try {
      await cardboardPricingAPI.deleteCardboardPricing(record.id);
      message.success('紙板單價已刪除');
      fetchPricingRecords(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error) {
      console.error('Failed to delete pricing record:', error);
      message.error('刪除失敗');
    }
  };

  // 表格欄位定義
  const columns = [
    {
      title: '時間 / Ngày',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('YYYY/MM/DD') : '-',
      sorter: (a: CardboardPricing, b: CardboardPricing) => {
        const dateA = a.date ? dayjs(a.date) : dayjs(0);
        const dateB = b.date ? dayjs(b.date) : dayjs(0);
        return dateA.valueOf() - dateB.valueOf();
      },
    },
    {
      title: '供應商代碼 / Mã nhà cung cấp',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 150,
    },
    {
      title: '材質克重 / Chất lượng (g)',
      dataIndex: 'material_weight',
      key: 'material_weight',
      width: 140,
      render: (weight: string | number) => weight ? `${weight}g` : '-',
    },
    {
      title: '材質代號 / Mã giấy',
      dataIndex: 'material_code',
      key: 'material_code',
      width: 120,
    },
    {
      title: '價格 / Đơn giá',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 120,
      render: (price: number, record: CardboardPricing) => 
        price ? `${record.currency} ${price.toLocaleString()}` : '-',
      sorter: (a: CardboardPricing, b: CardboardPricing) => (a.unit_price || 0) - (b.unit_price || 0),
    },
    {
      title: '瓦楞 / SÓNG',
      dataIndex: 'corrugated_type',
      key: 'corrugated_type',
      width: 100,
      render: (type: string) => type ? <Tag color="blue">{type}</Tag> : '-',
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const statusMap = {
          active: { color: 'green', text: '有效' },
          inactive: { color: 'red', text: '無效' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '生效日期',
      dataIndex: 'effective_date',
      key: 'effective_date',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('YYYY/MM/DD') : '-',
    },
    {
      title: '失效日期',
      dataIndex: 'expiry_date',
      key: 'expiry_date',
      width: 120,
      render: (date: string) => {
        if (!date) return '-';
        const expiry = dayjs(date);
        const isExpired = expiry.isBefore(dayjs(), 'day');
        return (
          <Text style={{ color: isExpired ? 'red' : 'inherit' }}>
            {expiry.format('YYYY/MM/DD')}
          </Text>
        );
      },
    },
    {
      title: '備註',
      dataIndex: 'notes',
      key: 'notes',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: CardboardPricing) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除這個紙板單價嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 統計卡片 */}
      {summary && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="總記錄" value={summary.total_records} prefix={<BarChartOutlined />} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="有效記錄" value={summary.active_records} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="供應商數" value={summary.supplier_count} prefix={<ShopOutlined />} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="平均價格" 
                value={summary.average_price} 
                prefix={<DollarOutlined />}
                formatter={(value) => `₫${Number(value).toLocaleString()}`}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>紙板單價管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜尋供應商代碼、材質代號或瓦楞類型"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder="供應商"
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="children"
              value={supplierFilter || undefined}
              onChange={(value) => setSupplierFilter(value || '')}
              filterOption={(input, option) => {
                const optionText = option?.children as string;
                return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
              }}
            >
              {suppliers.map(supplier => {
                const displayText = `${supplier.supplier_code} - ${supplier.company_name}`;
                return (
                  <Select.Option
                    key={supplier.id}
                    value={supplier.supplier_code}
                  >
                    {displayText}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="材質代號"
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="children"
              value={materialFilter || undefined}
              onChange={(value) => setMaterialFilter(value || '')}
              filterOption={(input, option) => {
                const optionText = option?.children as string;
                return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
              }}
            >
              {materials.map(material => {
                const displayText = `${material.material_code} - ${material.material_name}`;
                return (
                  <Select.Option
                    key={material.id}
                    value={material.material_code}
                  >
                    {displayText}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="active">有效</Select.Option>
              <Select.Option value="inactive">無效</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <RangePicker
              style={{ width: '100%' }}
              value={dateRange}
              onChange={(dates) => setDateRange(dates || [])}
              placeholder={['開始日期', '結束日期']}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增單價
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={pricingRecords}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯紙板單價' : '新增紙板單價'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={800}
          destroyOnHidden={true}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              currency: 'VND',
              status: 'active'
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="date"
                  label="時間 / Ngày"
                  rules={[{ required: true, message: '請選擇日期' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="supplier_code"
                  label="供應商代碼 / Mã nhà cung cấp"
                  rules={[{ required: true, message: '請選擇供應商' }]}
                >
                  <Select
                    placeholder="選擇供應商"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      const optionText = option?.children as string;
                      return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
                    }}
                  >
                    {suppliers.map(supplier => {
                      const displayText = `${supplier.supplier_code} - ${supplier.company_name}${supplier.contact_person ? ` (${supplier.contact_person})` : ''}`;
                      return (
                        <Select.Option
                          key={supplier.id}
                          value={supplier.supplier_code}
                        >
                          {displayText}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="material_weight"
                  label="材質克重 / Chất lượng (g)"
                  rules={[{ required: true, message: '請輸入材質克重' }]}
                >
                  <Input
                    style={{ width: '100%' }}
                    placeholder="例如: 120/90/90/90/120 或 250"
                    addonAfter="g"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="material_code"
                  label="材質代號 / Mã giấy"
                  rules={[{ required: true, message: '請輸入材質代號' }]}
                >
                  <Input
                    placeholder="輸入材質代號"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="unit_price"
                  label="價格 / Đơn giá"
                  rules={[{ required: true, message: '請輸入價格' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="價格"
                    min={0}
                    formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="corrugated_type"
                  label="瓦楞 / SÓNG"
                >
                  <Select
                    placeholder="選擇瓦楞類型"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      const optionText = option?.children as string;
                      return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
                    }}
                  >
                    {corrugatedTypes.map(corrugated => {
                      const displayText = `${corrugated.corrugated_code} - ${corrugated.corrugated_name}`;
                      return (
                        <Select.Option
                          key={corrugated.id}
                          value={corrugated.corrugated_code}
                        >
                          {displayText}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="currency"
                  label="貨幣"
                >
                  <Select>
                    <Select.Option value="VND">VND</Select.Option>
                    <Select.Option value="USD">USD</Select.Option>
                    <Select.Option value="CNY">CNY</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="status"
                  label="狀態"
                >
                  <Select>
                    <Select.Option value="active">有效</Select.Option>
                    <Select.Option value="inactive">無效</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="created_by"
                  label="建立者"
                >
                  <Input placeholder="建立者" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="effective_date"
                  label="生效日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="expiry_date"
                  label="失效日期"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="備註"
            >
              <TextArea rows={3} placeholder="輸入備註" />
            </Form.Item>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default CardboardPricingPage;
