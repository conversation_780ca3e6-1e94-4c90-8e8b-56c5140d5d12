import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Progress, List, Tag, Empty, Spin, Button, Space } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  ShopOutlined,
  TeamOutlined,
  RiseOutlined,
  AlertOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { dashboardAPI } from '../services/api';
import { message } from 'antd';

const { Title } = Typography;

interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  totalInventoryItems: number;
  totalEmployees: number;
}

interface WorkstationStatus {
  name: string;
  utilization: number;
  status: 'active' | 'idle' | 'maintenance';
}

interface RecentOrder {
  id: string;
  customer: string;
  amount: number;
  status: 'pending' | 'confirmed' | 'production' | 'completed';
}

interface SystemAlert {
  type: 'warning' | 'info' | 'error';
  message: string;
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalRevenue: 0,
    totalInventoryItems: 0,
    totalEmployees: 0
  });
  const [workstationStatus, setWorkstationStatus] = useState<WorkstationStatus[]>([]);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);

  // 獲取儀表板統計資料
  const fetchDashboardStats = async () => {
    setLoading(true);
    try {
      const response = await dashboardAPI.getStats();
      setStats({
        totalOrders: response.data.total_orders,
        totalRevenue: response.data.total_revenue,
        totalInventoryItems: response.data.total_inventory_items,
        totalEmployees: response.data.total_employees
      });
      message.success(t('messages.dataUpdated'));
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      message.error(t('messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 獲取工作站狀態
  const fetchWorkstationStatus = async () => {
    try {
      const response = await dashboardAPI.getWorkstationStatus();
      const workstationData = response.data.map((ws: any) => ({
        name: ws.name,
        utilization: ws.utilization,
        status: ws.status
      }));
      setWorkstationStatus(workstationData);
    } catch (error) {
      console.error('Failed to fetch workstation status:', error);
      message.error(t('messages.fetchWorkstationStatusFailed'));
    }
  };

  // 獲取最近訂單
  const fetchRecentOrders = async () => {
    try {
      const response = await dashboardAPI.getRecentOrders(10);
      const ordersData = response.data.map((order: any) => ({
        id: order.id.toString(),
        customer: order.customer,
        amount: order.amount,
        status: order.status
      }));
      setRecentOrders(ordersData);
    } catch (error) {
      console.error('Failed to fetch recent orders:', error);
      message.error(t('messages.fetchRecentOrdersFailed'));
    }
  };

  // 獲取系統警示
  const fetchSystemAlerts = async () => {
    try {
      const response = await dashboardAPI.getSystemAlerts();
      const alertsData = response.data.map((alert: any) => ({
        type: alert.type,
        message: alert.message
      }));
      setSystemAlerts(alertsData);
    } catch (error) {
      console.error('Failed to fetch system alerts:', error);
      message.error(t('messages.fetchSystemAlertsFailed'));
    }
  };

  // 獲取所有數據
  const fetchAllData = async () => {
    await Promise.all([
      fetchDashboardStats(),
      fetchWorkstationStatus(),
      fetchRecentOrders(),
      fetchSystemAlerts()
    ]);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const statsCards = [
    {
      title: t('customerOrders.title'),
      value: stats.totalOrders,
      prefix: <ShoppingCartOutlined />,
      suffix: t('common.items'),
      color: '#1890ff'
    },
    {
      title: t('finance.revenue'),
      value: stats.totalRevenue,
      prefix: <DollarOutlined />,
      suffix: 'VND',
      color: '#52c41a'
    },
    {
      title: t('inventory.title'),
      value: stats.totalInventoryItems,
      prefix: <ShopOutlined />,
      suffix: t('common.items'),
      color: '#faad14'
    },
    {
      title: t('hrPayroll.title'),
      value: stats.totalEmployees,
      prefix: <TeamOutlined />,
      suffix: t('common.people'),
      color: '#722ed1'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'confirmed': return 'blue';
      case 'production': return 'purple';
      case 'completed': return 'green';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t('customerOrders.pending');
      case 'confirmed': return t('customerOrders.confirmed');
      case 'production': return t('customerOrders.production');
      case 'completed': return t('customerOrders.completed');
      default: return status;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'orange';
      case 'error': return 'red';
      case 'info': return 'blue';
      default: return 'default';
    }
  };

  const getAlertText = (type: string) => {
    switch (type) {
      case 'warning': return t('common.warning');
      case 'error': return t('common.error');
      case 'info': return t('common.info');
      default: return type;
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>{t('navigation.dashboard')}</Title>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchAllData}
          loading={loading}
        >
          {t('common.refreshData')}
        </Button>
      </div>

      {/* 統計卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {statsCards.map((stat, index) => (
          <Col xs={24} sm={12} md={6} lg={6} key={index}>
            <Card>
              <Spin spinning={loading}>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  prefix={stat.prefix}
                  suffix={stat.suffix}
                  valueStyle={{ color: stat.color }}
                />
              </Spin>
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 工作站狀態 */}
        <Col xs={24} lg={12}>
          <Card title={`${t('productionSchedule.title')} - ${t('productionSchedule.utilization')}`} loading={loading}>
            {!loading && workstationStatus.length === 0 ? (
              <Empty
                description={t('dashboard.noWorkstationData')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : !loading ? (
              <List
                dataSource={workstationStatus}
                renderItem={(item) => (
                  <List.Item>
                    <div style={{ width: '100%' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginBottom: 8
                      }}>
                        <span>{item.name}</span>
                        <span>{item.utilization}%</span>
                      </div>
                      <Progress
                        percent={item.utilization}
                        status={item.status === 'active' ? 'active' : 'normal'}
                        strokeColor={item.utilization > 80 ? '#ff4d4f' : '#1890ff'}
                      />
                    </div>
                  </List.Item>
                )}
              />
            ) : null}
          </Card>
        </Col>

        {/* 最近訂單 */}
        <Col xs={24} lg={12}>
          <Card title={`${t('dashboard.recentOrders')}`} loading={loading}>
            {!loading && recentOrders.length === 0 ? (
              <Empty
                description={t('dashboard.noRecentOrders')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : !loading ? (
              <List
                dataSource={recentOrders}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>{item.id}</span>
                          <Tag color={getStatusColor(item.status)}>
                            {getStatusText(item.status)}
                          </Tag>
                        </div>
                      }
                      description={
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>{item.customer}</span>
                          <span style={{ fontWeight: 'bold' }}>{item.amount.toLocaleString()} VND</span>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : null}
          </Card>
        </Col>
      </Row>

      {/* 警示區域 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card
            title={
              <span>
                <AlertOutlined style={{ color: '#faad14', marginRight: 8 }} />
                {t('dashboard.systemAlerts')}
              </span>
            }
            loading={loading}
          >
            {!loading && systemAlerts.length === 0 ? (
              <Empty
                description={t('dashboard.noSystemAlerts')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : !loading ? (
              <List
                dataSource={systemAlerts}
                renderItem={(item) => (
                  <List.Item>
                    <Tag color={getAlertColor(item.type)}>
                      {getAlertText(item.type)}
                    </Tag>
                    {item.message}
                  </List.Item>
                )}
              />
            ) : null}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
