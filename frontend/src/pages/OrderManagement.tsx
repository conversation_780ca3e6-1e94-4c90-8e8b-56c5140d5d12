import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  TruckOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { orderManagementAPI, type OrderManagement, type OrderManagementCreate, type OrderManagementUpdate, type OrderSummary, type CustomerOption } from '../services/orderManagementAPI';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const OrderManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [orders, setOrders] = useState<OrderManagement[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [deliveryModalVisible, setDeliveryModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentOrder, setCurrentOrder] = useState<OrderManagement | null>(null);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [summary, setSummary] = useState<OrderSummary | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [orderStatusFilter, setOrderStatusFilter] = useState<string>('');
  const [productionStatusFilter, setProductionStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取訂單列表
  const fetchOrders = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(orderStatusFilter && { order_status: orderStatusFilter }),
        ...(productionStatusFilter && { production_status: productionStatusFilter }),
        ...(priorityFilter && { priority: priorityFilter }),
        ...(customerFilter && { customer_code: customerFilter }),
      };

      const response = await orderManagementAPI.getOrders(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setOrders(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      message.error('獲取訂單資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取統計資料和元數據
  const fetchMetadata = async () => {
    try {
      const [customersRes, categoriesRes, summaryRes] = await Promise.all([
        orderManagementAPI.getCustomersList(),
        orderManagementAPI.getCategoriesList(),
        orderManagementAPI.getOrderSummary()
      ]);
      setCustomers(customersRes.data);
      setCategories(categoriesRes.data);
      setSummary(summaryRes.data);
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchOrders();
    fetchMetadata();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchOrders(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setOrderStatusFilter('');
    setProductionStatusFilter('');
    setPriorityFilter('');
    setCustomerFilter('');
    setTimeout(() => {
      fetchOrders(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchOrders(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentOrder(null);
    form.resetFields();
    form.setFieldsValue({
      unit: 'PCS',
      order_status: 'active',
      production_status: 'pending',
      priority: 'normal',
      delivered_quantity: 0,
      order_received_date: dayjs()
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (order: OrderManagement) => {
    setEditMode(true);
    setCurrentOrder(order);
    form.setFieldsValue({
      ...order,
      order_received_date: order.order_received_date ? dayjs(order.order_received_date) : null,
      delivery_deadline: order.delivery_deadline ? dayjs(order.delivery_deadline) : null,
    });
    setModalVisible(true);
  };

  // 顯示交貨表單
  const showDeliveryForm = (order: OrderManagement) => {
    setCurrentOrder(order);
    setDeliveryModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      const orderData = {
        ...values,
        order_received_date: values.order_received_date?.format('YYYY-MM-DD'),
        delivery_deadline: values.delivery_deadline?.format('YYYY-MM-DD'),
      };

      if (editMode && currentOrder) {
        // 更新訂單
        await orderManagementAPI.updateOrder(currentOrder.id, orderData as OrderManagementUpdate);
        message.success('訂單更新成功');
      } else {
        // 新增訂單
        await orderManagementAPI.createOrder(orderData as OrderManagementCreate);
        message.success('訂單新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchOrders(pagination.current, pagination.pageSize);
      fetchMetadata(); // 重新獲取統計資料
    } catch (error: any) {
      console.error('Failed to save order:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 交貨處理
  const handleDelivery = async (values: any) => {
    if (!currentOrder) return;
    
    try {
      await orderManagementAPI.deliverOrder(
        currentOrder.id, 
        values.delivery_quantity, 
        values.delivery_note
      );
      message.success('交貨成功');
      setDeliveryModalVisible(false);
      fetchOrders(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error: any) {
      console.error('Failed to deliver order:', error);
      const errorMessage = error.response?.data?.detail || '交貨失敗';
      message.error(errorMessage);
    }
  };

  // 刪除訂單
  const handleDelete = async (order: OrderManagement) => {
    try {
      await orderManagementAPI.deleteOrder(order.id);
      message.success('訂單已刪除');
      fetchOrders(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error) {
      console.error('Failed to delete order:', error);
      message.error('刪除失敗');
    }
  };

  // 表格欄位定義
  const columns = [
    {
      title: '訂單編號 / số đơn dặt hàng',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 150,
      fixed: 'left' as const,
    },
    {
      title: '客戶代碼 / Mã khách hàng',
      dataIndex: 'customer_code',
      key: 'customer_code',
      width: 120,
    },
    {
      title: '接單日期 / Nhận đơn',
      dataIndex: 'order_received_date',
      key: 'order_received_date',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('YYYY/MM/DD') : '-',
    },
    {
      title: '指定送貨時間 / Phải giao hàng',
      dataIndex: 'delivery_deadline',
      key: 'delivery_deadline',
      width: 130,
      render: (date: string) => {
        if (!date) return '-';
        const deadline = dayjs(date);
        const isOverdue = deadline.isBefore(dayjs(), 'day');
        return (
          <Text style={{ color: isOverdue ? 'red' : 'inherit' }}>
            {deadline.format('YYYY/MM/DD')}
          </Text>
        );
      },
    },
    {
      title: '產品名稱 / Tên hàng hóa',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '品項 / Loại',
      dataIndex: 'product_category',
      key: 'product_category',
      width: 100,
      render: (category: string) => category ? <Tag color="blue">{category}</Tag> : '-',
    },
    {
      title: '規格 / Quy cách',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 150,
      ellipsis: true,
    },
    {
      title: '單位 / Đvt',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
    },
    {
      title: '訂单数量 / SL khách đặt',
      dataIndex: 'ordered_quantity',
      key: 'ordered_quantity',
      width: 120,
    },
    {
      title: '原物料採購 / số đặt giấy',
      dataIndex: 'paper_order_quantity',
      key: 'paper_order_quantity',
      width: 130,
    },
    {
      title: '紙板實際數量 / SL giấy Thực tế',
      dataIndex: 'actual_paper_quantity',
      key: 'actual_paper_quantity',
      width: 140,
    },
    {
      title: '印刷版 / bảng in',
      dataIndex: 'printing_plate',
      key: 'printing_plate',
      width: 120,
    },
    {
      title: '刀模號 / số khuôn',
      dataIndex: 'mold_number',
      key: 'mold_number',
      width: 120,
    },
    {
      title: '生產狀態 / Tình trạng sản xuất',
      dataIndex: 'production_status',
      key: 'production_status',
      width: 130,
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'orange', text: '待生產' },
          in_progress: { color: 'blue', text: '生產中' },
          completed: { color: 'green', text: '已完成' },
          cancelled: { color: 'red', text: '已取消' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '單價 / Đơn giá',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 120,
      render: (price: number) => price ? `₫${price.toLocaleString()}` : '-',
    },
    {
      title: '金額 / Thành tiền',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (amount: number) => amount ? `₫${amount.toLocaleString()}` : '-',
    },
    {
      title: '交货数量 / SL Giao hàng',
      dataIndex: 'delivered_quantity',
      key: 'delivered_quantity',
      width: 120,
      render: (quantity: number) => <Text style={{ color: 'green' }}>{quantity || 0}</Text>,
    },
    {
      title: '剩余订单总数 / Tổng đơn còn lại',
      dataIndex: 'remaining_quantity',
      key: 'remaining_quantity',
      width: 140,
      render: (quantity: number) => {
        const color = quantity > 0 ? 'orange' : 'green';
        return <Text style={{ color }}>{quantity || 0}</Text>;
      },
    },
    {
      title: '送貨單號 / phiếu giao hàng',
      dataIndex: 'delivery_note_number',
      key: 'delivery_note_number',
      width: 140,
    },
    {
      title: '備註 / Ghi chú',
      dataIndex: 'notes',
      key: 'notes',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      fixed: 'right' as const,
      render: (_: any, record: OrderManagement) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          {record.order_status === 'active' && (record.remaining_quantity || 0) > 0 && (
            <Button
              type="link"
              size="small"
              icon={<TruckOutlined />}
              onClick={() => showDeliveryForm(record)}
            >
              交貨
            </Button>
          )}
          <Popconfirm
            title="確定要刪除這個訂單嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 統計卡片 */}
      {summary && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="總訂單" value={summary.total_orders} prefix={<BarChartOutlined />} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="進行中" value={summary.active_orders} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已完成" value={summary.completed_orders} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="總金額" 
                value={summary.total_value} 
                prefix={<DollarOutlined />}
                formatter={(value) => `₫${Number(value).toLocaleString()}`}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>訂單管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜尋訂單編號、產品名稱或刀模號"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="客戶"
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="children"
              value={customerFilter || undefined}
              onChange={(value) => setCustomerFilter(value || '')}
              filterOption={(input, option) => {
                const optionText = option?.children as string;
                return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
              }}
            >
              {customers.map(customer => {
                const displayText = `${customer.customer_code} - ${customer.company_name}`;
                return (
                  <Select.Option
                    key={customer.id}
                    value={customer.customer_code}
                  >
                    {displayText}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="訂單狀態"
              style={{ width: '100%' }}
              allowClear
              value={orderStatusFilter || undefined}
              onChange={(value) => setOrderStatusFilter(value || '')}
            >
              <Select.Option value="active">進行中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
              <Select.Option value="cancelled">已取消</Select.Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="生產狀態"
              style={{ width: '100%' }}
              allowClear
              value={productionStatusFilter || undefined}
              onChange={(value) => setProductionStatusFilter(value || '')}
            >
              <Select.Option value="pending">待生產</Select.Option>
              <Select.Option value="in_progress">生產中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
            </Select>
          </Col>
          <Col span={2}>
            <Select
              placeholder="優先級"
              style={{ width: '100%' }}
              allowClear
              value={priorityFilter || undefined}
              onChange={(value) => setPriorityFilter(value || '')}
            >
              <Select.Option value="high">高</Select.Option>
              <Select.Option value="normal">中</Select.Option>
              <Select.Option value="low">低</Select.Option>
            </Select>
          </Col>
          <Col span={7}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增訂單
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 2800 }}
          size="small"
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯訂單' : '新增訂單'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={1400}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              unit: 'PCS',
              order_status: 'active',
              production_status: 'pending',
              priority: 'normal',
              delivered_quantity: 0
            }}
          >
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="order_number"
                  label="訂單編號 / số đơn dặt hàng"
                  rules={[{ required: true, message: '請輸入訂單編號' }]}
                >
                  <Input placeholder="訂單編號" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="customer_code"
                  label="客戶代碼 / Mã khách hàng"
                >
                  <Select
                    placeholder="選擇客戶"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      const optionText = option?.children as string;
                      return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
                    }}
                  >
                    {customers.map(customer => {
                      const displayText = `${customer.customer_code} - ${customer.company_name}${customer.contact_person ? ` (${customer.contact_person})` : ''}`;
                      return (
                        <Select.Option
                          key={customer.id}
                          value={customer.customer_code}
                        >
                          {displayText}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="order_received_date"
                  label="接單日期 / Nhận đơn"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="delivery_deadline"
                  label="指定送貨時間 / Phải giao hàng"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="product_name"
                  label="產品名稱 / Tên hàng hóa"
                >
                  <Input placeholder="產品名稱" />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  name="product_category"
                  label="品項 / Loại"
                >
                  <Select
                    placeholder="選擇或輸入品項"
                    showSearch
                    allowClear
                    mode="combobox"
                  >
                    {categories.map(category => (
                      <Select.Option key={category} value={category}>{category}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="specifications"
                  label="規格 / Quy cách"
                >
                  <Input placeholder="規格" />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  name="unit"
                  label="單位 / Đvt"
                >
                  <Select>
                    <Select.Option value="PCS">PCS</Select.Option>
                    <Select.Option value="SET">SET</Select.Option>
                    <Select.Option value="BOX">BOX</Select.Option>
                    <Select.Option value="KG">KG</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="ordered_quantity"
                  label="訂单数量 / SL khách đặt"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="訂单数量" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="paper_order_quantity"
                  label="原物料採購 / số đặt giấy"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="原物料採購" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="actual_paper_quantity"
                  label="紙板實際數量 / SL giấy Thực tế"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="紙板實際數量" min={0} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="printing_plate"
                  label="印刷版 / bảng in"
                >
                  <Input placeholder="印刷版" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="mold_number"
                  label="刀模號 / số khuôn"
                >
                  <Input placeholder="刀模號" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="production_status"
                  label="生產狀態 / Tình trạng sản xuất"
                >
                  <Select>
                    <Select.Option value="pending">待生產</Select.Option>
                    <Select.Option value="in_progress">生產中</Select.Option>
                    <Select.Option value="completed">已完成</Select.Option>
                    <Select.Option value="cancelled">已取消</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="unit_price"
                  label="單價 / Đơn giá"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="單價"
                    min={0}
                    formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="delivered_quantity"
                  label="交货数量 / SL Giao hàng"
                >
                  <InputNumber style={{ width: '100%' }} placeholder="交货数量" min={0} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="delivery_note_number"
                  label="送貨單號 / phiếu giao hàng"
                >
                  <Input placeholder="送貨單號" />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  name="order_status"
                  label="訂單狀態"
                >
                  <Select>
                    <Select.Option value="active">進行中</Select.Option>
                    <Select.Option value="completed">已完成</Select.Option>
                    <Select.Option value="cancelled">已取消</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  name="priority"
                  label="優先級"
                >
                  <Select>
                    <Select.Option value="high">高</Select.Option>
                    <Select.Option value="normal">中</Select.Option>
                    <Select.Option value="low">低</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="created_by"
                  label="建立者"
                >
                  <Input placeholder="建立者" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="備註 / Ghi chú"
            >
              <TextArea rows={3} placeholder="輸入備註" />
            </Form.Item>

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 交貨 Modal */}
        <Modal
          title="交貨"
          open={deliveryModalVisible}
          onCancel={() => setDeliveryModalVisible(false)}
          footer={null}
          width={500}
        >
          {currentOrder && (
            <Form onFinish={handleDelivery} layout="vertical">
              <div style={{ marginBottom: 16 }}>
                <Text strong>訂單編號：</Text><Text>{currentOrder.order_number}</Text><br />
                <Text strong>產品名稱：</Text><Text>{currentOrder.product_name}</Text><br />
                <Text strong>剩餘數量：</Text><Text style={{ color: 'orange' }}>{currentOrder.remaining_quantity}</Text>
              </div>

              <Form.Item
                name="delivery_quantity"
                label="交貨數量"
                rules={[
                  { required: true, message: '請輸入交貨數量' },
                  {
                    type: 'number',
                    max: currentOrder.remaining_quantity || 0,
                    message: `交貨數量不能超過剩餘數量 ${currentOrder.remaining_quantity}`
                  }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="交貨數量"
                  min={1}
                  max={currentOrder.remaining_quantity || 0}
                />
              </Form.Item>

              <Form.Item
                name="delivery_note"
                label="送貨單號"
              >
                <Input placeholder="輸入送貨單號" />
              </Form.Item>

              <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
                <Space>
                  <Button onClick={() => setDeliveryModalVisible(false)}>
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit">
                    確認交貨
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          )}
        </Modal>
      </Card>
    </div>
  );
};

export default OrderManagementPage;
