import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  TimePicker,
  Badge,
  Descriptions,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  ClockCircleOutlined,
  UserOutlined,
  DollarOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;


interface Employee {
  id: string;
  employeeCode: string;
  name: string;
  department: string;
  position: string;
  hireDate: string;
  hourlyRate: number;
  monthlySalary: number;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface AttendanceRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  workHours: number;
  overtimeHours: number;
  status: string;
  notes: string;
}

interface PayrollRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  payPeriod: string;
  regularHours: number;
  overtimeHours: number;
  regularPay: number;
  overtimePay: number;
  bonus: number;
  deductions: number;
  grossPay: number;
  netPay: number;
}

const HRPayroll: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [form] = Form.useForm();

  // Employee data state
  const [employees, setEmployees] = useState<Employee[]>([]);

  // Fetch employee data from API
  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/hr-payroll/employees`);
      if (response.ok) {
        const data = await response.json();
        setEmployees(data);
      } else {
        message.error(t('hrPayroll.fetchEmployeesFailed'));
      }
    } catch (error) {
      console.error('Failed to fetch employee data:', error);
      message.error(t('messages.networkError'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchEmployees();
    fetchPayrollRecords();
    fetchHrStats();
  }, []);

  // Attendance records state
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);

  // Payroll records state
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([]);

  // HR statistics data state
  const [hrStats, setHrStats] = useState({
    attendanceRate: 0,
    totalSalary: 0,
    averageSalary: 0
  });

  // Fetch payroll records
  const fetchPayrollRecords = async () => {
    try {
      // TODO: Implement API call
      // const response = await fetch('/api/hr-payroll/payroll-records');
      // const data = await response.json();
      // setPayrollRecords(data);

      // Temporarily set to empty data
      setPayrollRecords([]);
    } catch (error) {
      console.error('Failed to fetch payroll records:', error);
    }
  };

  // Fetch HR statistics data
  const fetchHrStats = async () => {
    try {
      // TODO: Implement API call
      // const response = await fetch('/api/hr-payroll/stats');
      // const data = await response.json();
      // setHrStats(data);

      // Temporarily set to empty data
      setHrStats({
        attendanceRate: 0,
        totalSalary: 0,
        averageSalary: 0
      });
    } catch (error) {
      console.error('Failed to fetch HR statistics data:', error);
    }
  };

  const getAttendanceStatus = (status: string) => {
    switch (status) {
      case 'present': return { color: 'green', text: t('hrPayroll.present') };
      case 'late': return { color: 'orange', text: t('hrPayroll.late') };
      case 'early_leave': return { color: 'orange', text: t('hrPayroll.earlyLeave') };
      case 'absent': return { color: 'red', text: t('hrPayroll.absent') };
      default: return { color: 'default', text: status };
    }
  };

  const employeeColumns: ColumnsType<Employee> = [
    {
      title: t('hrPayroll.employeeCode'),
      dataIndex: 'employeeCode',
      key: 'employeeCode',
      width: 120,
    },
    {
      title: t('hrPayroll.employeeName'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('hrPayroll.department'),
      dataIndex: 'department',
      key: 'department',
      width: 100,
    },
    {
      title: t('hrPayroll.position'),
      dataIndex: 'position',
      key: 'position',
      width: 100,
    },
    {
      title: t('hrPayroll.hireDate'),
      dataIndex: 'hireDate',
      key: 'hireDate',
      width: 120,
    },
    {
      title: t('hrPayroll.hourlyRate'),
      dataIndex: 'hourlyRate',
      key: 'hourlyRate',
      width: 100,
      render: (rate: number) => `${rate.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.monthlySalary'),
      dataIndex: 'monthlySalary',
      key: 'monthlySalary',
      width: 120,
      render: (salary: number) => `${salary.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.status'),
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? t('hrPayroll.active') : t('hrPayroll.inactive')}
        />
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
        </Space>
      ),
    },
  ];

  const attendanceColumns: ColumnsType<AttendanceRecord> = [
    {
      title: t('hrPayroll.date'),
      dataIndex: 'date',
      key: 'date',
      width: 120,
    },
    {
      title: t('hrPayroll.employeeName'),
      dataIndex: 'employeeName',
      key: 'employeeName',
    },
    {
      title: t('hrPayroll.checkIn'),
      dataIndex: 'checkInTime',
      key: 'checkInTime',
      width: 100,
      render: (time: string | null) => time || '-',
    },
    {
      title: t('hrPayroll.checkOut'),
      dataIndex: 'checkOutTime',
      key: 'checkOutTime',
      width: 100,
      render: (time: string | null) => time || '-',
    },
    {
      title: t('hrPayroll.workHours'),
      dataIndex: 'workHours',
      key: 'workHours',
      width: 100,
      render: (hours: number) => `${hours} ${t('hrPayroll.hours')}`,
    },
    {
      title: t('hrPayroll.overtimeHours'),
      dataIndex: 'overtimeHours',
      key: 'overtimeHours',
      width: 100,
      render: (hours: number) => `${hours} ${t('hrPayroll.hours')}`,
    },
    {
      title: t('hrPayroll.attendanceStatus'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusInfo = getAttendanceStatus(status);
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: t('hrPayroll.notes'),
      dataIndex: 'notes',
      key: 'notes',
    },
  ];

  const payrollColumns: ColumnsType<PayrollRecord> = [
    {
      title: t('hrPayroll.employeeName'),
      dataIndex: 'employeeName',
      key: 'employeeName',
    },
    {
      title: t('hrPayroll.payPeriod'),
      dataIndex: 'payPeriod',
      key: 'payPeriod',
      width: 100,
    },
    {
      title: t('hrPayroll.regularHours'),
      dataIndex: 'regularHours',
      key: 'regularHours',
      width: 100,
      render: (hours: number) => `${hours} ${t('hrPayroll.hours')}`,
    },
    {
      title: t('hrPayroll.overtimeHours'),
      dataIndex: 'overtimeHours',
      key: 'overtimeHours',
      width: 100,
      render: (hours: number) => `${hours} ${t('hrPayroll.hours')}`,
    },
    {
      title: t('hrPayroll.regularPay'),
      dataIndex: 'regularPay',
      key: 'regularPay',
      width: 120,
      render: (pay: number) => `${pay.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.overtimePay'),
      dataIndex: 'overtimePay',
      key: 'overtimePay',
      width: 100,
      render: (pay: number) => `${pay.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.bonus'),
      dataIndex: 'bonus',
      key: 'bonus',
      width: 100,
      render: (bonus: number) => `${bonus.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.deductions'),
      dataIndex: 'deductions',
      key: 'deductions',
      width: 100,
      render: (deductions: number) => `${deductions.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.grossPay'),
      dataIndex: 'grossPay',
      key: 'grossPay',
      width: 120,
      render: (pay: number) => `${pay.toLocaleString()} VND`,
    },
    {
      title: t('hrPayroll.netPay'),
      dataIndex: 'netPay',
      key: 'netPay',
      width: 120,
      render: (pay: number) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          {pay.toLocaleString()} VND
        </span>
      ),
    },
  ];

  const handleView = (record: Employee) => {
    setSelectedEmployee(record);
    setDetailModalVisible(true);
  };

  const handleEdit = (record: Employee) => {
    setSelectedEmployee(record);
    setIsModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleAdd = () => {
    setSelectedEmployee(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then((values) => {
      if (selectedEmployee) {
        // Edit existing employee
        const updatedEmployees = employees.map(emp =>
          emp.id === selectedEmployee.id
            ? { ...emp, ...values, updatedAt: new Date().toISOString() }
            : emp
        );
        setEmployees(updatedEmployees);
        message.success(t('hrPayroll.employeeUpdated'));
      } else {
        // Add new employee
        const newEmployee: Employee = {
          id: Date.now().toString(),
          ...values,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setEmployees([...employees, newEmployee]);
        message.success(t('hrPayroll.employeeAdded'));
      }
      setIsModalVisible(false);
      form.resetFields();
      setSelectedEmployee(null);
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div>
      <Title level={2}>{t('hrPayroll.title')}</Title>
      
      {/* Statistics cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('hrPayroll.totalEmployees')}
              value={employees.length}
              suffix={t('hrPayroll.employeeUnit')}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('hrPayroll.todayAttendanceRate')}
              value={hrStats.attendanceRate}
              suffix="%"
              valueStyle={{ color: '#3f8600' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('hrPayroll.monthlyTotalSalary')}
              value={hrStats.totalSalary}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('hrPayroll.averageSalary')}
              value={hrStats.averageSalary}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs
          defaultActiveKey="employees"
          items={[
            {
              key: "employees",
              label: t('hrPayroll.employeeManagement'),
              children: (
                <>
            {/* Search and filter area */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Input
                  placeholder={t('hrPayroll.searchEmployeePlaceholder')}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder={t('hrPayroll.filterDepartment')}
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Select.Option value="生產部">{t('hrPayroll.productionDept')}</Select.Option>
                  <Select.Option value="品管部">{t('hrPayroll.qualityDept')}</Select.Option>
                  <Select.Option value="管理部">{t('hrPayroll.managementDept')}</Select.Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder={t('hrPayroll.employeeStatus')}
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Select.Option value={true}>{t('hrPayroll.active')}</Select.Option>
                  <Select.Option value={false}>{t('hrPayroll.inactive')}</Select.Option>
                </Select>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  style={{ width: '100%' }}
                >
                  {t('hrPayroll.addEmployee')}
                </Button>
              </Col>
            </Row>

            {/* Employee table */}
            <Table
              rowSelection={rowSelection}
              columns={employeeColumns}
              dataSource={employees}
              rowKey="id"
              loading={loading}
              pagination={{
                total: employees.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
              }}
            />
                </>
              )
            },
            {
              key: "attendance",
              label: t('hrPayroll.attendance'),
              children: (
                <>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <RangePicker style={{ width: '100%' }} />
              </Col>
              <Col span={6}>
                <Select
                  placeholder={t('hrPayroll.selectEmployee')}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {employees.map(emp => (
                    <Select.Option key={emp.id} value={emp.id}>
                      {emp.name}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Button type="primary" icon={<CalendarOutlined />}>
                  {t('hrPayroll.queryAttendance')}
                </Button>
              </Col>
            </Row>

            <Table
              columns={attendanceColumns}
              dataSource={attendanceRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                total: attendanceRecords.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
              }}
            />
                </>
              )
            },
            {
              key: "payroll",
              label: t('hrPayroll.payrollManagement'),
              children: (
                <>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <DatePicker.MonthPicker 
                  placeholder={t('hrPayroll.selectPayrollMonth')}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={6}>
                <Button type="primary" icon={<DollarOutlined />}>
                  {t('hrPayroll.calculateSalary')}
                </Button>
              </Col>
            </Row>

            <Table
              columns={payrollColumns}
              dataSource={payrollRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                total: payrollRecords.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
              }}
            />
                </>
              )
            }
          ]}
        />
      </Card>

      {/* Add/Edit employee dialog */}
      <Modal
        title={form.getFieldValue('id') ? t('hrPayroll.editEmployee') : t('hrPayroll.addEmployee')}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="employeeCode"
                label={t('hrPayroll.employeeCode')}
                rules={[{ required: true, message: t('hrPayroll.employeeCodeRequired') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('hrPayroll.employeeName')}
                rules={[{ required: true, message: t('hrPayroll.employeeNameRequired') }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department"
                label={t('hrPayroll.department')}
                rules={[{ required: true, message: t('hrPayroll.departmentRequired') }]}
              >
                <Select>
                  <Select.Option value="生產部">{t('hrPayroll.productionDept')}</Select.Option>
                  <Select.Option value="品管部">{t('hrPayroll.qualityDept')}</Select.Option>
                  <Select.Option value="管理部">{t('hrPayroll.managementDept')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="position"
                label={t('hrPayroll.position')}
                rules={[{ required: true, message: t('hrPayroll.positionRequired') }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="hireDate"
                label={t('hrPayroll.hireDate')}
                rules={[{ required: true, message: t('hrPayroll.hireDateRequired') }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="hourlyRate"
                label={t('hrPayroll.hourlyRate')}
                rules={[{ required: true, message: t('hrPayroll.hourlyRateRequired') }]}
              >
                <Input type="number" prefix="NT$" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="monthlySalary"
                label={t('hrPayroll.monthlySalary')}
                rules={[{ required: true, message: t('hrPayroll.monthlySalaryRequired') }]}
              >
                <Input type="number" prefix="NT$" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Employee details modal */}
      <Modal
        title={t('hrPayroll.employeeDetails')}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={600}
      >
        {selectedEmployee && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label={t('hrPayroll.employeeCode')}>
              {selectedEmployee.employeeCode}
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.employeeName')}>
              {selectedEmployee.name}
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.department')}>
              {selectedEmployee.department}
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.position')}>
              {selectedEmployee.position}
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.hireDate')}>
              {selectedEmployee.hireDate}
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.status')}>
              <Tag color={selectedEmployee.isActive ? 'green' : 'red'}>
                {selectedEmployee.isActive ? t('hrPayroll.active') : t('hrPayroll.inactive')}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.hourlyRate')}>
              {selectedEmployee.hourlyRate?.toLocaleString()} VND
            </Descriptions.Item>
            <Descriptions.Item label={t('hrPayroll.monthlySalary')}>
              {selectedEmployee.monthlySalary?.toLocaleString()} VND
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default HRPayroll;
