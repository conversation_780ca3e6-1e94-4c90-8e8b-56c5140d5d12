import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Descriptions,
  List,
  Avatar,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  BarChartOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { customerManagementAPI } from '../services/api';

const { Title } = Typography;
// 移除 TabPane 的使用，改用 items 屬性

interface Customer {
  id: string;
  customer_code: string;
  company_name: string;
  contact_person: string;
  phone: string;
  email: string;
  address: string;
  tax_id: string;
  payment_terms: number;
  credit_limit: number;
  status: string;
  total_orders: number;
  total_sales_amount: number;
  current_month_sales: number;
  last_month_sales: number;
  created_at: string;
  updated_at: string;
}

interface CustomerSalesAnalysis {
  customerId: string;
  customerName: string;
  totalOrders: number;
  totalSalesAmount: number;
  currentMonthSales: number;
  lastMonthSales: number;
  growthRate: number;
  averageOrderValue: number;
  topProducts: Array<{
    productName: string;
    quantity: number;
    amount: number;
  }>;
}

const CustomerManagement: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAnalysisModalVisible, setIsAnalysisModalVisible] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [creditFilter, setCreditFilter] = useState('');
  const [form] = Form.useForm();

  // 客戶數據狀態
  const [customers, setCustomers] = useState<Customer[]>([]);

  // 分頁狀態 - 設置為顯示全部客戶
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 100, // 增加到100以確保顯示全部客戶
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
  });

  // 從API獲取客戶數據
  const fetchCustomers = async (page = 1, pageSize = 100, filters = {}) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...filters,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      };

      const response = await customerManagementAPI.getCustomers(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      // 轉換資料格式以符合前端介面
      // 直接使用 API 回傳的資料格式，無需轉換
      setCustomers(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total: total,
      }));
      message.success(t('messages.dataLoaded', { count: data.length, total }));
    } catch (error) {
      console.error('Failed to fetch customer data:', error);
      message.error(t('messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 組件掛載時獲取數據
  useEffect(() => {
    fetchCustomers();
  }, []);

  // 處理分頁變更
  const handleTableChange = (page: number, pageSize?: number) => {
    fetchCustomers(page, pageSize || pagination.pageSize);
  };

  // 處理搜尋
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCustomers(1, pagination.pageSize);
  };

  // 模擬銷售分析數據
  const salesAnalysis: CustomerSalesAnalysis = {
    customerId: '1',
    customerName: 'ABC公司',
    totalOrders: 25,
    totalSalesAmount: 500000,
    currentMonthSales: 50000,
    lastMonthSales: 45000,
    growthRate: 11.11,
    averageOrderValue: 20000,
    topProducts: [
      { productName: '包裝盒A', quantity: 5000, amount: 75000 },
      { productName: '標籤B', quantity: 10000, amount: 30000 },
      { productName: '手提袋C', quantity: 1000, amount: 25000 }
    ]
  };

  // 篩選客戶
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchTerm ||
      customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.customerCode.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !statusFilter || customer.status === statusFilter;

    // 簡單的信用狀況判斷
    const creditStatus = customer.status === 'active' ? 'normal' : 'warning';
    const matchesCredit = !creditFilter || creditStatus === creditFilter;

    return matchesSearch && matchesStatus && matchesCredit;
  });

  // 清除篩選
  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setCreditFilter('');
    message.success(t('messages.filtersCleared'));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'potential': return 'blue';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return t('customerManagement.active');
      case 'inactive': return t('customerManagement.inactive');
      case 'potential': return t('customerManagement.potential');
      default: return status;
    }
  };

  const getCreditStatus = (currentAmount: number, creditLimit: number) => {
    if (!creditLimit || creditLimit === 0) {
      return { color: 'gray', text: t('customerManagement.notSetup') };
    }
    const usage = (currentAmount / creditLimit) * 100;
    if (usage >= 90) return { color: 'red', text: t('customerManagement.creditInsufficient') };
    if (usage >= 70) return { color: 'orange', text: t('customerManagement.creditHigh') };
    return { color: 'green', text: t('customerManagement.creditNormal') };
  };

  const customerColumns: ColumnsType<Customer> = [
    {
      title: t('customerManagement.customerCode'),
      dataIndex: 'customer_code',
      key: 'customer_code',
      width: 120,
    },
    {
      title: t('customerManagement.companyName'),
      dataIndex: 'company_name',
      key: 'company_name',
    },
    {
      title: t('customerManagement.contactPerson'),
      dataIndex: 'contact_person',
      key: 'contact_person',
      width: 100,
    },
    {
      title: t('customerManagement.phone'),
      dataIndex: 'phone',
      key: 'phone',
      width: 130,
    },
    {
      title: t('customerManagement.totalOrders'),
      dataIndex: 'total_orders',
      key: 'total_orders',
      width: 100,
      render: (orders: number) => `${orders || 0} ${t('common.items')}`,
    },
    {
      title: t('customerManagement.totalSales'),
      dataIndex: 'total_sales_amount',
      key: 'total_sales_amount',
      width: 120,
      render: (amount: number) => `${(amount || 0).toLocaleString()} VND`,
    },
    {
      title: t('customerManagement.monthlySales'),
      dataIndex: 'current_month_sales',
      key: 'current_month_sales',
      width: 120,
      render: (amount: number) => (
        <span style={{ color: (amount || 0) > 0 ? '#52c41a' : '#999' }}>
          {(amount || 0).toLocaleString()} VND
        </span>
      ),
    },
    {
      title: t('customerManagement.creditLimit'),
      key: 'creditStatus',
      width: 120,
      render: (_, record) => {
        const creditLimit = record.credit_limit || 0;
        const currentMonthSales = record.current_month_sales || 0;
        const status = getCreditStatus(currentMonthSales, creditLimit);
        return (
          <div>
            <div>{creditLimit.toLocaleString()} VND</div>
            <Tag color={status.color} size="small">{status.text}</Tag>
          </div>
        );
      },
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            icon={<BarChartOutlined />}
            onClick={() => handleSalesAnalysis(record)}
          />
          <Button
            type="text"
            icon={<ShoppingOutlined />}
            onClick={() => handleOrderHistory(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  const handleView = (record: Customer) => {
    Modal.info({
      title: `${record.companyName} - 客戶詳情`,
      width: 600,
      content: (
        <Descriptions column={2} bordered size="small">
          <Descriptions.Item label={t('customerManagement.customerCode')}>{record.customerCode}</Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.companyName')}>{record.companyName}</Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.contactPerson')} span={2}>
            <Space>
              <UserOutlined />
              {record.contactPerson}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.phone')}>
            <Space>
              <PhoneOutlined />
              {record.phone}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.email')}>
            <Space>
              <MailOutlined />
              {record.email}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.address')} span={2}>
            <Space>
              <HomeOutlined />
              {record.address}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.taxId')}>{record.taxId}</Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.paymentTerms')}>{record.paymentTerms} {t('common.days')}</Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.creditLimit')}>{record.creditLimit.toLocaleString()} VND</Descriptions.Item>
          <Descriptions.Item label={t('customerManagement.lastOrderDate')}>{record.lastOrderDate}</Descriptions.Item>
        </Descriptions>
      ),
    });
  };

  const handleEdit = (record: Customer) => {
    console.log('編輯客戶:', record);
    setIsModalVisible(true);
    form.setFieldsValue(record);
  };

  const handleSalesAnalysis = (record: Customer) => {
    setSelectedCustomer(record);
    setIsAnalysisModalVisible(true);
  };

  const handleOrderHistory = (record: Customer) => {
    console.log('查看訂單歷史:', record);
    // 這裡可以導航到客戶訂單頁面或顯示訂單列表
  };

  const handleDelete = (record: Customer) => {
    Modal.confirm({
      title: '確認刪除',
      content: `確定要刪除客戶 ${record.companyName} 嗎？`,
      onOk() {
        console.log('刪除客戶:', record);
      },
    });
  };

  const handleAdd = () => {
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then((values) => {
      console.log('表單數據:', values);
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div>
      <Title level={2}>{t('customerManagement.title')}</Title>
      
      {/* 統計卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('customerManagement.totalCustomers')}
              value={pagination.total}
              suffix={t('common.companies')}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('customerManagement.activeCustomers')}
              value={customers.filter(c => c.status === 'active').length}
              suffix={t('common.companies')}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('customerManagement.totalSales')}
              value={customers.reduce((sum, c) => sum + (c.total_sales_amount || 0), 0)}
              suffix="VND"
              valueStyle={{ color: '#1890ff' }}
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('customerManagement.monthlySales')}
              value={customers.reduce((sum, c) => sum + (c.current_month_sales || 0), 0)}
              suffix="VND"
              valueStyle={{ color: '#722ed1' }}
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div>
            {/* 搜尋和篩選區域 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Input
                  placeholder={t('customerManagement.searchPlaceholder')}
                  prefix={<SearchOutlined />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onPressEnter={handleSearch}
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder={t('customerManagement.filterStatus')}
                  style={{ width: '100%' }}
                  allowClear
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                >
                  <Select.Option value="active">{t('customerManagement.active')}</Select.Option>
                  <Select.Option value="inactive">{t('customerManagement.inactive')}</Select.Option>
                  <Select.Option value="potential">{t('customerManagement.potential')}</Select.Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder={t('customerManagement.creditStatus')}
                  style={{ width: '100%' }}
                  allowClear
                  value={creditFilter}
                  onChange={(value) => setCreditFilter(value || '')}
                >
                  <Select.Option value="normal">{t('customerManagement.creditNormal')}</Select.Option>
                  <Select.Option value="warning">{t('customerManagement.creditWarning')}</Select.Option>
                  <Select.Option value="danger">{t('customerManagement.creditDanger')}</Select.Option>
                </Select>
              </Col>
              <Col span={6}>
                <Space style={{ width: '100%' }}>
                  <Button onClick={clearFilters}>
                    {t('common.clearFilters')}
                  </Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAdd}
                  >
                    {t('customerManagement.addCustomer')}
                  </Button>
                </Space>
              </Col>
            </Row>

            {/* 客戶表格 */}
            <Table
              rowSelection={rowSelection}
              columns={customerColumns}
              dataSource={customers}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                onChange: handleTableChange,
                onShowSizeChange: handleTableChange,
              }}
            />
        </div>
      </Card>

      {/* 新增/編輯客戶對話框 */}
      <Modal
        title={form.getFieldValue('id') ? '編輯客戶' : '新增客戶'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customerCode"
                label="客戶代碼"
                rules={[{ required: true, message: '請輸入客戶代碼' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="companyName"
                label="公司名稱"
                rules={[{ required: true, message: '請輸入公司名稱' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="contactPerson"
                label="聯絡人"
                rules={[{ required: true, message: '請輸入聯絡人' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="phone"
                label="電話"
                rules={[{ required: true, message: '請輸入電話' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="email"
                label="電子郵件"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="address"
                label="地址"
                rules={[{ required: true, message: '請輸入地址' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="taxId"
                label="統一編號"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="paymentTerms"
                label="付款條件(天)"
                rules={[{ required: true, message: '請輸入付款條件' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="creditLimit"
                label="信用額度"
                rules={[{ required: true, message: '請輸入信用額度' }]}
              >
                <Input type="number" addonAfter="VND" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 銷售分析對話框 */}
      <Modal
        title={`${selectedCustomer?.companyName} - 銷售分析`}
        open={isAnalysisModalVisible}
        onCancel={() => setIsAnalysisModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setIsAnalysisModalVisible(false)}>
            {t('common.close')}
          </Button>,
        ]}
      >
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Statistic
              title="總訂單數"
              value={salesAnalysis.totalOrders}
              suffix="筆"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="總銷售額"
              value={salesAnalysis.totalSalesAmount}
              prefix="NT$"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均訂單金額"
              value={salesAnalysis.averageOrderValue}
              prefix="NT$"
            />
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Statistic
              title="本月銷售"
              value={salesAnalysis.currentMonthSales}
              prefix="NT$"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="上月銷售"
              value={salesAnalysis.lastMonthSales}
              prefix="NT$"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="成長率"
              value={salesAnalysis.growthRate}
              suffix="%"
              valueStyle={{ color: salesAnalysis.growthRate > 0 ? '#3f8600' : '#cf1322' }}
            />
          </Col>
        </Row>

        <Title level={5}>{t('customerAnalysis.topProducts')}</Title>
        <List
          dataSource={salesAnalysis.topProducts}
          renderItem={(item, index) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar>{index + 1}</Avatar>}
                title={item.productName}
                description={`數量: ${item.quantity.toLocaleString()} | 金額: NT$ ${item.amount.toLocaleString()}`}
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default CustomerManagement;
