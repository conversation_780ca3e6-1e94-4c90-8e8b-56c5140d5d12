import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  message,
  Modal
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import DetailModal from '../components/DetailModal';
import FormModal from '../components/FormModal';
import SearchFilters from '../components/SearchFilters';

const { Title } = Typography;
const { confirm } = Modal;

interface MaterialOrder {
  id: string;
  orderNumber: string;
  supplierName: string;
  orderDate: string;
  expectedDeliveryDate: string;
  actualDeliveryDate?: string;
  status: string;
  totalAmount: number;
  itemCount: number;
  items: Array<{
    materialName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

const MaterialOrders: React.FC = () => {
  const { t } = useTranslation();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<MaterialOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<MaterialOrder | null>(null);



  // 狀態管理
  const [orders, setOrders] = useState<MaterialOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<any>({});

  // 從後端獲取原物料訂單數據
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/material-orders/`);
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
      } else {
        message.error(t('messages.fetchDataFailed'));
      }
    } catch (error) {
      console.error('Failed to fetch material orders:', error);
      message.error(t('messages.networkError'));
    } finally {
      setLoading(false);
    }
  };

  // 組件掛載時獲取數據
  useEffect(() => {
    fetchOrders();
  }, []);

  // 篩選訂單
  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchTerm ||
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.supplierName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status || order.status === filters.status;

    return matchesSearch && matchesStatus;
  });

  const setFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    console.log('Clear filters called');
    console.log('Before clear - searchTerm:', searchTerm, 'filters:', filters);
    setFilters({});
    setSearchTerm('');
    console.log('After clear - should be empty');
    message.success(t('messages.filtersCleared'));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'ordered': return 'blue';
      case 'received': return 'green';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t('materialOrders.pending');
      case 'ordered': return t('materialOrders.ordered');
      case 'received': return t('materialOrders.received');
      case 'cancelled': return t('common.cancelled');
      default: return status;
    }
  };

  // 處理查看詳情
  const handleView = (record: MaterialOrder) => {
    setSelectedOrder(record);
    setDetailVisible(true);
  };

  // 處理編輯
  const handleEdit = (record: MaterialOrder) => {
    setEditingOrder(record);
    setFormVisible(true);
  };

  // 處理刪除
  const handleDelete = async (record: MaterialOrder) => {
    const confirmed = window.confirm(t('materialOrders.deleteConfirm', { orderNumber: record.orderNumber }));
    if (!confirmed) return;

    try {
      // 暫時使用本地刪除，等後端 API 實現後再改為 API 調用
      setOrders(orders.filter(order => order.id !== record.id));
      message.success(t('messages.deleteSuccess'));
    } catch (error) {
      console.error('Delete order error:', error);
      message.error(t('messages.deleteFailed'));
    }
  };

  // 處理新增
  const handleAdd = () => {
    setEditingOrder(null);
    setFormVisible(true);
  };

  // 處理表單提交
  const handleFormSubmit = (values: any) => {
    const orderData = {
      ...values,
      orderNumber: editingOrder ? editingOrder.orderNumber : `MAT-${Date.now()}`,
      itemCount: editingOrder ? editingOrder.itemCount : 0,
      items: editingOrder ? editingOrder.items : [],
      createdAt: editingOrder ? editingOrder.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingOrder) {
      updateItem(editingOrder.id, orderData);
      message.success(t('messages.updateSuccess'));
    } else {
      addItem(orderData);
      message.success(t('messages.addSuccess'));
    }
    
    setFormVisible(false);
    setEditingOrder(null);
  };

  // 批量刪除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('materialOrders.selectOrdersToDelete'));
      return;
    }

    const confirmed = window.confirm(t('materialOrders.batchDeleteConfirm', { count: selectedRowKeys.length }));
    if (!confirmed) return;

    try {
      // 暫時使用本地刪除，等後端 API 實現後再改為 API 調用
      setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));
      setSelectedRowKeys([]);
      message.success(t('materialOrders.batchDeleteSuccess', { count: selectedRowKeys.length }));
    } catch (error) {
      console.error('Batch delete error:', error);
      message.error(t('materialOrders.batchDeleteFailed'));
    }
  };

  // 搜尋篩選欄位配置
  const filterFields = [
    {
      key: 'search',
      label: t('common.search'),
      type: 'search' as const,
      placeholder: t('materialOrders.searchPlaceholder'),
      span: 8
    },
    {
      key: 'status',
      label: t('common.status'),
      type: 'select' as const,
      options: [
        { label: t('materialOrders.pending'), value: 'pending' },
        { label: t('materialOrders.ordered'), value: 'ordered' },
        { label: t('materialOrders.received'), value: 'received' },
        { label: t('common.cancelled'), value: 'cancelled' }
      ],
      span: 6
    },
    {
      key: 'dateRange',
      label: t('materialOrders.dateRange'),
      type: 'dateRange' as const,
      span: 8
    }
  ];

  // 表單欄位配置
  const formFields = [
    { name: 'supplierName', label: t('materialOrders.supplierName'), type: 'text' as const, required: true, span: 12 },
    { name: 'orderDate', label: t('materialOrders.orderDate'), type: 'date' as const, required: true, span: 12 },
    { name: 'expectedDeliveryDate', label: t('materialOrders.expectedDeliveryDate'), type: 'date' as const, required: true, span: 12 },
    { name: 'actualDeliveryDate', label: t('materialOrders.actualDeliveryDate'), type: 'date' as const, span: 12 },
    { name: 'totalAmount', label: t('materialOrders.totalAmount'), type: 'number' as const, required: true, span: 12 },
    { name: 'status', label: t('common.status'), type: 'select' as const, required: true, span: 12,
      options: [
        { label: t('materialOrders.pending'), value: 'pending' },
        { label: t('materialOrders.ordered'), value: 'ordered' },
        { label: t('materialOrders.received'), value: 'received' },
        { label: t('common.cancelled'), value: 'cancelled' }
      ]
    },
    { name: 'notes', label: t('materialOrders.notes'), type: 'textarea' as const, span: 24 }
  ];

  // 詳情欄位配置
  const detailFields = [
    { label: t('materialOrders.orderNumber'), key: 'orderNumber' },
    { label: t('materialOrders.supplierName'), key: 'supplierName' },
    { label: t('materialOrders.orderDate'), key: 'orderDate', type: 'date' as const },
    { label: t('materialOrders.expectedDeliveryDate'), key: 'expectedDeliveryDate', type: 'date' as const },
    { label: t('materialOrders.actualDeliveryDate'), key: 'actualDeliveryDate', type: 'date' as const },
    { label: t('common.status'), key: 'status', type: 'tag' as const },
    { label: t('materialOrders.totalAmount'), key: 'totalAmount', type: 'currency' as const },
    { label: t('materialOrders.itemCount'), key: 'itemCount' },
    { label: t('materialOrders.notes'), key: 'notes', span: 2 },
    { label: t('materialOrders.createdAt'), key: 'createdAt', type: 'date' as const },
    { label: t('materialOrders.updatedAt'), key: 'updatedAt', type: 'date' as const }
  ];

  const columns: ColumnsType<MaterialOrder> = [
    {
      title: t('materialOrders.orderNumber'),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
    },
    {
      title: t('materialOrders.supplierName'),
      dataIndex: 'supplierName',
      key: 'supplierName',
    },
    {
      title: t('materialOrders.orderDate'),
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
    },
    {
      title: t('materialOrders.expectedDeliveryDate'),
      dataIndex: 'expectedDeliveryDate',
      key: 'expectedDeliveryDate',
      width: 140,
    },
    {
      title: t('materialOrders.actualDeliveryDate'),
      dataIndex: 'actualDeliveryDate',
      key: 'actualDeliveryDate',
      width: 140,
      render: (date: string) => date || '-',
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: t('materialOrders.totalAmount'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('materialOrders.itemCount'),
      dataIndex: 'itemCount',
      key: 'itemCount',
      width: 100,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            title={t('materialOrders.viewDetails')}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title={t('materialOrders.edit')}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            title={t('common.delete')}
          />
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div>
      <Title level={2}>{t('materialOrders.title')}</Title>
      
      {/* 統計卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('materialOrders.totalOrders')}
              value={orders.length}
              suffix={t('common.items')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('materialOrders.pendingDelivery')}
              value={orders.filter(o => o.status === 'ordered').length}
              suffix={t('common.items')}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('materialOrders.totalPurchaseAmount')}
              value={orders.reduce((sum, o) => sum + o.totalAmount, 0)}
              suffix="VND"
              valueStyle={{ color: '#3f8600' }}
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('materialOrders.averageOrderAmount')}
              value={orders.length > 0 ? Math.round(orders.reduce((sum, o) => sum + o.totalAmount, 0) / orders.length) : 0}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 搜尋和篩選區域 */}
        <SearchFilters
          fields={filterFields}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filters={filters}
          onFilterChange={setFilter}
          onClear={clearFilters}
          onAdd={handleAdd}
          addButtonText={t('materialOrders.addPurchase')}
        />

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Space>
              <span>{t('materialOrders.selectedItems', { count: selectedRowKeys.length })}</span>
              <Button danger onClick={handleBatchDelete}>
                {t('materialOrders.batchDelete')}
              </Button>
            </Space>
          </div>
        )}

        {/* 表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
          }}
        />
      </Card>

      {/* 詳情查看對話框 */}
      <DetailModal
        title={`${selectedOrder?.orderNumber} - ${t('materialOrders.orderDetails')}`}
        visible={detailVisible}
        onClose={() => setDetailVisible(false)}
        data={selectedOrder}
        fields={detailFields}
      />

      {/* 新增/編輯對話框 */}
      <FormModal
        title={editingOrder ? t('materialOrders.editOrder') : t('materialOrders.addOrder')}
        visible={formVisible}
        onOk={handleFormSubmit}
        onCancel={() => {
          setFormVisible(false);
          setEditingOrder(null);
        }}
        fields={formFields}
        initialValues={editingOrder}
      />
    </div>
  );
};

export default MaterialOrders;
