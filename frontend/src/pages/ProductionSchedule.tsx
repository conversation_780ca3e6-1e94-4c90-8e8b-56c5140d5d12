import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Progress,
  Tag,
  List,
  Button,
  Space,
  DatePicker,
  Select,
  Statistic,
  Badge,
  Tooltip,
  Modal,
  Table,
  Form,
  Input,
  TimePicker,
  message,
  Empty,
  Spin
} from 'antd';
import dayjs from 'dayjs';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { productionScheduleAPI } from '../services/api';

const { Title, Text } = Typography;

interface WorkstationStatus {
  id: string;
  name: string;
  vietnameseName: string;
  status: 'active' | 'idle' | 'maintenance' | 'offline';
  utilization: number;
  currentTask?: {
    orderId: string;
    customerName: string;
    productName: string;
    progress: number;
    estimatedCompletion: string;
  };
  pendingTasks: number;
  dailyCapacity: number;
  todayProduced: number;
}

const ProductionSchedule: React.FC = () => {
  const { t } = useTranslation();
  const [selectedDate, setSelectedDate] = useState(dayjs('2024-01-20'));
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [scheduleModalVisible, setScheduleModalVisible] = useState(false);
  const [selectedWorkstation, setSelectedWorkstation] = useState<WorkstationStatus | null>(null);
  const [selectedWorkstationFilter, setSelectedWorkstationFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [form] = Form.useForm();

  // 工作站數據狀態
  const [workstations, setWorkstations] = useState<WorkstationStatus[]>([]);
  const [loading, setLoading] = useState(false);

  // 獲取工作站狀態
  const fetchWorkstations = async () => {
    setLoading(true);
    try {
      const response = await productionScheduleAPI.getWorkstations();
      const workstationsData = response.data.map((ws: any) => ({
        id: ws.id.toString(),
        name: ws.name,
        vietnameseName: ws.vietnamese_name,
        status: ws.status,
        utilization: ws.utilizationRate,
        pendingTasks: ws.pendingTasks,
        dailyCapacity: ws.dailyCapacity,
        todayProduced: ws.todayProduced
      }));
      setWorkstations(workstationsData);
      message.success(t('messages.dataUpdated'));
    } catch (error) {
      console.error('Failed to fetch workstation status:', error);
      message.error(t('messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [selectedDate]);

  // 處理日期變更
  const handleDateChange = (date: any) => {
    setSelectedDate(date);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <PlayCircleOutlined style={{ color: '#52c41a' }} />;
      case 'idle':
        return <PauseCircleOutlined style={{ color: '#faad14' }} />;
      case 'maintenance':
        return <WarningOutlined style={{ color: '#ff4d4f' }} />;
      case 'offline':
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'idle': return 'warning';
      case 'maintenance': return 'error';
      case 'offline': return 'default';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return t('productionSchedule.running');
      case 'idle': return t('productionSchedule.idle');
      case 'maintenance': return t('productionSchedule.maintenance');
      case 'offline': return t('productionSchedule.offline');
      case 'pending': return t('common.pending');
      case 'in_progress': return t('common.inProgress');
      case 'completed': return t('common.completed');
      case 'cancelled': return t('common.cancelled');
      default: return status;
    }
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return '#ff4d4f';
    if (utilization >= 70) return '#faad14';
    return '#52c41a';
  };

  const totalCapacity = workstations.reduce((sum, ws) => sum + ws.dailyCapacity, 0);
  const totalProduced = workstations.reduce((sum, ws) => sum + ws.todayProduced, 0);
  const activeStations = workstations.filter(ws => ws.status === 'active').length;
  const totalTasks = workstations.reduce((sum, ws) => sum + ws.pendingTasks, 0);

  // 處理查看詳情
  const handleViewDetails = (workstation: WorkstationStatus) => {
    setSelectedWorkstation(workstation);
    setDetailModalVisible(true);
  };

  // 處理調整排程
  const handleAdjustSchedule = (workstation: WorkstationStatus) => {
    setSelectedWorkstation(workstation);
    form.setFieldsValue({
      workstationName: workstation.name,
      dailyCapacity: workstation.dailyCapacity,
      status: workstation.status
    });
    setScheduleModalVisible(true);
  };

  // 保存排程調整
  const handleSaveSchedule = async () => {
    try {
      const values = await form.validateFields();

      // 更新工作站數據
      if (selectedWorkstation) {
        const updatedWorkstations = workstations.map(ws =>
          ws.id === selectedWorkstation.id
            ? {
                ...ws,
                dailyCapacity: values.dailyCapacity,
                status: values.status,
                updatedAt: new Date().toISOString()
              }
            : ws
        );
        setWorkstations(updatedWorkstations);
      }

      message.success(t('productionSchedule.scheduleAdjusted'));
      setScheduleModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
      message.error(t('productionSchedule.saveFailed'));
    }
  };

  // 清除篩選
  const clearFilters = () => {
    setSelectedWorkstationFilter('');
    setStatusFilter('');
    message.success(t('messages.filtersCleared'));
  };

  // 任務數據狀態
  const [tasks, setTasks] = useState([]);

  // 獲取生產任務
  const fetchTasks = async () => {
    try {
      const params = {
        date: selectedDate.format('YYYY-MM-DD')
      };
      const response = await productionScheduleAPI.getTasks(params);
      const tasksData = response.data.map((task: any) => ({
        id: task.id,
        orderId: task.order_id,
        customerName: task.customer_name,
        productName: task.product_name,
        quantity: task.quantity,
        startTime: task.start_time ? dayjs(task.start_time).format('HH:mm') : '-',
        endTime: task.end_time ? dayjs(task.end_time).format('HH:mm') : '-',
        status: getStatusText(task.status),
        progress: task.status === 'completed' ? 100 :
                 task.status === 'in_progress' ? 50 : 0,
        workstation: task.workstation,
        priority: task.priority,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
        assignedOperator: task.assigned_operator,
        notes: task.notes
      }));
      setTasks(tasksData);
    } catch (error) {
      console.error('Failed to fetch production tasks:', error);
      message.error(t('messages.fetchTasksFailed'));
    }
  };

  // 獲取所有數據
  const fetchAllData = async () => {
    await Promise.all([
      fetchWorkstations(),
      fetchTasks()
    ]);
  };

  const taskColumns = [
    {
      title: t('orderManagement.orderNumber'),
      dataIndex: 'orderId',
      key: 'orderId',
    },
    {
      title: t('orderManagement.customer'),
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: t('orderManagement.productName'),
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: t('orderManagement.orderQuantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('productionSchedule.startTime'),
      dataIndex: 'startTime',
      key: 'startTime',
    },
    {
      title: t('productionSchedule.endTime'),
      dataIndex: 'endTime',
      key: 'endTime',
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === t('common.inProgress') ? 'processing' : 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: t('common.progress'),
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    }
  ];

  return (
    <div>
      <Title level={2}>{t('productionSchedule.title')}</Title>
      
      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <DatePicker
            value={selectedDate}
            onChange={(date) => setSelectedDate(date || dayjs())}
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={6}>
          <Select
            placeholder={t('productionSchedule.selectWorkstation')}
            style={{ width: '100%' }}
            allowClear
            value={selectedWorkstationFilter}
            onChange={(value) => setSelectedWorkstationFilter(value || '')}
          >
            {workstations.map(ws => (
              <Select.Option key={ws.id} value={ws.id}>
                {ws.name}
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder={t('productionSchedule.filterStatus')}
            style={{ width: '100%' }}
            allowClear
            value={statusFilter}
            onChange={(value) => setStatusFilter(value || '')}
          >
            <Select.Option value="active">{t('productionSchedule.running')}</Select.Option>
            <Select.Option value="idle">{t('productionSchedule.idle')}</Select.Option>
            <Select.Option value="maintenance">{t('productionSchedule.maintenance')}</Select.Option>
          </Select>
        </Col>
        <Col span={3}>
          <Button onClick={clearFilters} style={{ width: '100%' }}>
            {t('common.clearFilters')}
          </Button>
        </Col>
        <Col span={3}>
          <Button
            type="primary"
            onClick={fetchAllData}
            loading={loading}
            style={{ width: '100%' }}
          >
            {t('common.refreshData')}
          </Button>
        </Col>
      </Row>

      {/* 總覽統計 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('productionSchedule.activeWorkstations')}
              value={activeStations}
              suffix={`/ ${workstations.length}`}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('productionSchedule.todayProduction')}
              value={totalProduced}
              suffix={`/ ${totalCapacity}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('productionSchedule.utilizationRate')}
              value={Math.round((totalProduced / totalCapacity) * 100)}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('productionSchedule.pendingTasks')}
              value={totalTasks}
              suffix={t('common.items')}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 工作站狀態卡片 */}
      <Row gutter={[16, 16]}>
        {loading ? (
          // 顯示loading骨架
          Array.from({ length: 6 }).map((_, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card loading={true} size="small" />
            </Col>
          ))
        ) : workstations.length > 0 ? (
          workstations.map((workstation) => (
            <Col xs={24} sm={12} lg={8} key={workstation.id}>
              <Card
                title={
                  <Space>
                    {getStatusIcon(workstation.status)}
                    <span>{workstation.name}</span>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      ({workstation.vietnameseName})
                    </Text>
                  </Space>
                }
                extra={
                  <Badge
                    status={getStatusColor(workstation.status)}
                    text={getStatusText(workstation.status)}
                  />
                }
                size="small"
              >
              {/* 使用率 */}
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text strong>{t('productionSchedule.utilization')}</Text>
                  <Text>{workstation.utilization}%</Text>
                </div>
                <Progress
                  percent={workstation.utilization}
                  strokeColor={getUtilizationColor(workstation.utilization)}
                  size="small"
                />
              </div>

              {/* 當前任務 */}
              {workstation.currentTask && (
                <div style={{ marginBottom: 16 }}>
                  <Text strong>{t('productionSchedule.currentTask')}</Text>
                  <div style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    marginTop: '4px'
                  }}>
                    <div style={{ fontSize: '12px' }}>
                      <div>{t('orderManagement.orderNumber')}: {workstation.currentTask.orderId}</div>
                      <div>{t('orderManagement.customer')}: {workstation.currentTask.customerName}</div>
                      <div>{t('orderManagement.productName')}: {workstation.currentTask.productName}</div>
                      <div style={{ marginTop: '4px' }}>
                        <Progress
                          percent={workstation.currentTask.progress}
                          size="small"
                          format={(percent) => `${percent}%`}
                        />
                      </div>
                      <div style={{ marginTop: '4px' }}>
                        {t('productionSchedule.estimatedCompletion')}: {workstation.currentTask.estimatedCompletion}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 統計資訊 */}
              <Row gutter={8}>
                <Col span={8}>
                  <Statistic
                    title={t('common.pending')}
                    value={workstation.pendingTasks}
                    suffix={t('common.items')}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('productionSchedule.todayProduced')}
                    value={workstation.todayProduced}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('productionSchedule.dailyCapacity')}
                    value={workstation.dailyCapacity}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
              </Row>

              {/* 操作按鈕 */}
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Space>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => handleViewDetails(workstation)}
                  >
                    {t('productionSchedule.viewDetails')}
                  </Button>
                  <Button
                    size="small"
                    onClick={() => handleAdjustSchedule(workstation)}
                  >
                    {t('productionSchedule.adjustSchedule')}
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>
          ))
        ) : (
          <Col span={24}>
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">{t('productionSchedule.noWorkstationData')}</Text>
              </div>
            </Card>
          </Col>
        )}
      </Row>

      {/* 詳情查看模態框 */}
      <Modal
        title={`${selectedWorkstation?.name} - ${t('productionSchedule.workstationDetails')}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={800}
      >
        {selectedWorkstation && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card size="small" title={t('productionSchedule.workstationInfo')}>
                  <p><strong>{t('productionSchedule.name')}:</strong> {selectedWorkstation.name}</p>
                  <p><strong>{t('productionSchedule.vietnameseName')}:</strong> {selectedWorkstation.vietnameseName}</p>
                  <p><strong>{t('common.status')}:</strong> <Tag color={getStatusColor(selectedWorkstation.status)}>{getStatusText(selectedWorkstation.status)}</Tag></p>
                  <p><strong>{t('productionSchedule.utilization')}:</strong> {selectedWorkstation.utilization}%</p>
                  <p><strong>{t('productionSchedule.dailyCapacity')}:</strong> {selectedWorkstation.dailyCapacity}</p>
                  <p><strong>{t('productionSchedule.todayProduced')}:</strong> {selectedWorkstation.todayProduced}</p>
                </Card>
              </Col>
              <Col span={12}>
                {selectedWorkstation.currentTask && (
                  <Card size="small" title={t('productionSchedule.currentTask')}>
                    <p><strong>{t('orderManagement.orderNumber')}:</strong> {selectedWorkstation.currentTask.orderId}</p>
                    <p><strong>{t('orderManagement.customer')}:</strong> {selectedWorkstation.currentTask.customerName}</p>
                    <p><strong>{t('orderManagement.productName')}:</strong> {selectedWorkstation.currentTask.productName}</p>
                    <p><strong>{t('common.progress')}:</strong> {selectedWorkstation.currentTask.progress}%</p>
                    <p><strong>{t('productionSchedule.estimatedCompletion')}:</strong> {selectedWorkstation.currentTask.estimatedCompletion}</p>
                  </Card>
                )}
              </Col>
            </Row>
            <Card
              size="small"
              title={t('productionSchedule.todaySchedule')}
              extra={
                <Button
                  size="small"
                  onClick={fetchAllData}
                  loading={loading}
                >
                  {t('common.refresh')}
                </Button>
              }
            >
              <Table
                columns={taskColumns}
                dataSource={tasks}
                pagination={false}
                size="small"
                loading={loading}
                locale={{
                  emptyText: tasks.length === 0 && !loading ? t('productionSchedule.noTaskData') : undefined
                }}
              />
            </Card>
          </div>
        )}
      </Modal>

      {/* 調整排程模態框 */}
      <Modal
        title={`${t('productionSchedule.adjustSchedule')} - ${selectedWorkstation?.name}`}
        open={scheduleModalVisible}
        onOk={handleSaveSchedule}
        onCancel={() => {
          setScheduleModalVisible(false);
          form.resetFields();
        }}
        okText={t('common.save')}
        cancelText={t('common.cancel')}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="workstationName"
            label={t('productionSchedule.workstationName')}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="dailyCapacity"
            label={t('productionSchedule.dailyCapacity')}
            rules={[{ required: true, message: t('productionSchedule.dailyCapacityRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="status"
            label={t('common.status')}
            rules={[{ required: true, message: t('productionSchedule.statusRequired') }]}
          >
            <Select>
              <Select.Option value="active">{t('productionSchedule.running')}</Select.Option>
              <Select.Option value="idle">{t('productionSchedule.idle')}</Select.Option>
              <Select.Option value="maintenance">{t('productionSchedule.maintenance')}</Select.Option>
              <Select.Option value="offline">{t('productionSchedule.offline')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="maintenanceTime"
            label={t('productionSchedule.maintenanceTime')}
          >
            <TimePicker.RangePicker />
          </Form.Item>
          <Form.Item
            name="notes"
            label={t('common.notes')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductionSchedule;
