export interface QuoteItem {
  id: string;
  order: number;
  product_code: string;
  product_name: string;
  category: string;
  specifications: string;
  unit: string;
  quantity: number;
  unit_price: number;
  old_price?: number;
  new_price?: number;
  paper_material: string;
  moq: number;
  sticker_label: number;
  die_cutting: number;
  printing_fee: number;
  notes: string;
}

export interface QuoteConfig {
  // 公司信息
  company_name_zh: string;
  company_name_vi: string;
  address: string;
  tax_number: string;
  phone: string;
  
  // 客戶信息
  customer_code: string;
  customer_name: string;
  customer_tax_number: string;
  customer_address: string;
  
  // 報價信息
  quote_date: string;
  
  // 條款設置
  include_printing_lamination: boolean;
  vat_8_percent: boolean;
  vat_10_percent: boolean;
  minimum_order_amount: boolean;
  payment_method_deposit: boolean;
  payment_method_monthly: boolean;
  payment_method_monthly_end: boolean;
  payment_method_30_days: boolean;
  delivery_location_warehouse: boolean;
  delivery_location_pickup: boolean;
  delivery_location_custom: string;
  quote_validity: boolean;
  delivery_time_7_10: boolean;
  delivery_time_5_7: boolean;
}

export interface QuoteData {
  config: QuoteConfig;
  items: QuoteItem[];
}
