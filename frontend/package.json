{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.26.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.0", "react-router-dom": "^7.7.0", "react-to-print": "^3.1.1", "recharts": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}